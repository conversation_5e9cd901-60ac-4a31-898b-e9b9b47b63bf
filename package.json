{"name": "quick-shot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --experimental-https --experimental-https-key ./localhost-key.pem --experimental-https-cert ./localhost.pem", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@mikecousins/react-pdf": "^7.1.0", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.6.0", "@sendgrid/mail": "^8.1.5", "@slack/socket-mode": "^2.0.4", "@slack/web-api": "^7.9.1", "canvas": "^3.0.1", "chart.js": "^4.4.8", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "firebase": "^11.1.0", "firebase-admin": "^13.2.0", "jspdf": "^2.5.2", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "next": "15.1.1", "nodemailer": "^6.9.16", "papaparse": "^5.4.1", "pdf-lib": "^1.17.1", "pdfkit": "^0.15.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "uuid": "^11.1.0"}, "devDependencies": {"postcss": "^8", "tailwindcss": "^3.4.1"}}