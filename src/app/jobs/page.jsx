"use client"

import { useEffect, useState, useRef } from "react"
import { collection, getDocs, doc, updateDoc, deleteDoc, onSnapshot, getDoc, deleteField, setDoc } from "firebase/firestore"
import { db } from "../../firebase"
import { useRouter } from "next/navigation"
import AssignJobModal from "../../components/modals/AssignJobModal"
import EmergencyJobModal from "@/components/modals/EmergencyJobModal"
import StatusPickerWithColor from "@/components/StatusPickerWithColor"
import {
  FaPaperclip,
  FaTrashAlt,
  FaCheckCircle,
  FaFileInvoice,
  FaEye,
  FaEdit,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaExclamationTriangle,
  FaStickyNote,
  FaPaperPlane,
  FaComment,
  FaTimes,
} from "react-icons/fa"
import { FaPersonCirclePlus } from "react-icons/fa6"
import { <PERSON><PERSON><PERSON>lineEye, AiOutlineEdit, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/ai"
import * as Toolt<PERSON> from "@radix-ui/react-tooltip"
import { useSelector, useDispatch } from "react-redux"
import dayjs from "dayjs"
import PriorityPicker from "@/components/PriorityPicker"
import ColumnSelectorModal from "@/components/modals/ColumnSelectorModal"
import NotesPopup from "@/components/modals/NotesPopup"
import SubcontractorSelectionModal from "@/components/modals/SubcontractorSelectionModal"
import ETAPicker from "@/components/ETAPicker"
import ETAPickerModal from "@/components/modals/ETAPickerModal"
import { getTimezoneFromLocation, getTimezoneAbbr } from "@/utils/locationToTimezone"
import { setColumnPreferences } from "@/slices/userPreferencesSlice"
// import SlackIntegration from "@/components/SlackIntegration"

// FRANK: Assigned To column doens't show the name of the assigned user.a .
// maybe refactor the assignment function to include the user object
// FRANK: remove the key on the dashnoard graph

const CommentsPopup = ({ comments, isOpen, jobId, companyId, onMouseEnter, onMouseLeave }) => {
  const [newComment, setNewComment] = useState("")
  const [replyText, setReplyText] = useState({})
  const [loading, setLoading] = useState(false)
  const { user } = useSelector(state => state.auth)
  const currentUser = user
  const popupRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = event => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onMouseLeave()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onMouseLeave])

  const handleAddComment = async () => {
    if (!newComment.trim()) return
    if (!currentUser) {
      alert("You must be logged in to comment.")
      return
    }

    setLoading(true)
    const updatedComments = [
      ...(Array.isArray(comments) ? comments : []),
      {
        author: {
          uid: currentUser.id || "",
          name: currentUser.name || "",
          email: currentUser.email || "",
        },
        text: newComment.trim(),
        timestamp: new Date().toISOString(),
        replies: [],
      },
    ]

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await updateDoc(docRef, { comments: updatedComments })
      setNewComment("")
    } catch (err) {
      console.error("Error adding comment:", err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleReply = async index => {
    if (!replyText[index]?.trim()) return
    if (!currentUser) {
      alert("You must be logged in to reply.")
      return
    }

    setLoading(true)
    const updatedComments = [...(Array.isArray(comments) ? comments : [])]
    if (!updatedComments[index]) return

    if (!updatedComments[index].replies) {
      updatedComments[index].replies = []
    }

    updatedComments[index].replies.push({
      author: {
        uid: currentUser.uid || "",
        name: currentUser.name || "",
        email: currentUser.email || "",
      },
      text: replyText[index].trim(),
      timestamp: new Date().toISOString(),
    })

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await updateDoc(docRef, { comments: updatedComments })
      setReplyText({ ...replyText, [index]: "" })
    } catch (err) {
      console.error("Error adding reply:", err.message)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div ref={popupRef} className="absolute right-0 top-4 w-80 bg-white border border-gray-300 rounded shadow-lg z-10 p-4">
      <h3 className="font-bold text-gray-900 mb-2 flex items-center">
        Comments {comments && comments.length > 0 && <span className="text-sm text-gray-700 ml-1">({comments.length})</span>}
      </h3>

      {/* Add Comment Section */}
      <div className="flex items-center border-b pb-4 mb-4">
        <textarea
          type="text"
          placeholder="Add a comment..."
          value={newComment}
          onChange={e => setNewComment(e.target.value)}
          className="w-full border border-gray-300 rounded p-2 text-gray-900"
          rows={2}
        />
        <button
          onClick={handleAddComment}
          disabled={loading || !newComment.trim()}
          className={`ml-2 p-2 rounded ${loading ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-600"} ${
            newComment.trim() ? "bg-blue-500 text-white hover:bg-blue-600" : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }`}
        >
          <FaPaperPlane />
        </button>
      </div>

      {/* Comments List */}
      {comments && comments.length > 0 ? (
        <div className="max-h-96 overflow-y-auto pr-2">
          {comments.map((comment, index) => (
            <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
              <p className="font-bold text-gray-900">
                {comment.author?.name || comment.author?.email}{" "}
                <span className="text-gray-500 text-sm">• {dayjs(comment.timestamp).format("M/D/YY")}</span>
              </p>
              <p className="text-gray-700">{comment.text}</p>

              {/* Replies Section */}
              {comment.replies && comment.replies.length > 0 && (
                <div className="ml-4 mt-2 border-l-2 pl-2">
                  {comment.replies.map((reply, replyIndex) => (
                    <div key={replyIndex} className="mb-2 bg-gray-100 p-2 rounded">
                      <p className="font-bold text-gray-900">
                        {reply.author?.name || reply.author?.email}{" "}
                        <span className="text-gray-500 text-sm">• {dayjs(reply.timestamp).format("M/D/YY")}</span>
                      </p>
                      <p className="text-gray-700">{reply.text}</p>
                    </div>
                  ))}
                </div>
              )}

              {/* Add Reply Input */}
              <div className="flex items-center mt-2">
                <textarea
                  type="text"
                  placeholder="Reply..."
                  value={replyText[index] || ""}
                  onChange={e => setReplyText({ ...replyText, [index]: e.target.value })}
                  className="w-full border border-gray-300 rounded p-2 text-gray-900"
                  rows={1}
                />
                <button
                  onClick={() => handleReply(index)}
                  disabled={loading || !replyText[index]?.trim()}
                  className={`ml-2 p-2 rounded ${loading ? "opacity-50 cursor-not-allowed" : "hover:bg-green-600"} ${
                    replyText[index]?.trim() ? "bg-green-500 text-white hover:bg-green-600" : "bg-gray-300 text-gray-500 cursor-not-allowed"
                  }`}
                >
                  <FaPaperPlane />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500 italic">No comments yet.</p>
      )}
    </div>
  )
}

export default function JobsList() {
  const [jobs, setJobs] = useState([])
  const [filteredJobs, setFilteredJobs] = useState([])
  const [filters, setFilters] = useState({
    searchQuery: "",
    status: [],
    location: "All Locations",
    startDate: "",
    endDate: "",
  })
  const [sortConfig, setSortConfig] = useState({ key: "createdAt", direction: "descending" })
  const [currentPage, setCurrentPage] = useState(1)
  const [assignModalOpen, setAssignModalOpen] = useState(false)
  const [selectedJobId, setSelectedJobId] = useState(null)
  const [emergencyModalOpen, setEmergencyModalOpen] = useState(false)
  const [jobType, setJobType] = useState(null)
  const [dropdownOpen, setDropdownOpen] = useState(null)
  const [resultsPerPage, setResultsPerPage] = useState(20)
  const [jobStatuses, setJobStatuses] = useState([])
  const jobsPerPage = resultsPerPage
  const router = useRouter()
  const dropdownRef = useRef(null)
  const { companyId } = useSelector(state => state.company)
  const [hoveredJobId, setHoveredJobId] = useState(null)
  const [hoveredJobIdForNotes, setHoveredJobIdForNotes] = useState(null)
  const [notesPopupPosition, setNotesPopupPosition] = useState({ top: 0, left: 0 })
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false)
  const statusDropdownRef = useRef(null)
  const [isMouseOverDropdown, setIsMouseOverDropdown] = useState(false)
  const [isColumnSelectorOpen, setIsColumnSelectorOpen] = useState(false)
  const [visibleColumns, setVisibleColumns] = useState([])
  const [orderedColumns, setOrderedColumns] = useState([])
  const { user } = useSelector(state => state.auth)
  const [subcontractorModalOpen, setSubcontractorModalOpen] = useState(false)
  const [selectedJobForSubcontractor, setSelectedJobForSubcontractor] = useState(null)
  const [etaModalOpen, setEtaModalOpen] = useState(false)
  const [selectedJobForETA, setSelectedJobForETA] = useState(null)
  const dispatch = useDispatch()

  if (!companyId) {
    return <div>Loading...</div>
  }

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = event => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        !event.target.closest(".dropdown-content") // Ensure clicks on dropdown options don't close it
      ) {
        setDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (!companyId) return
    const fetchJobs = () => {
      const unsubscribe = onSnapshot(collection(db, `companies/${companyId}/jobs`), async snapshot => {
        const jobsList = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))

        const jobsWithDetails = await Promise.all(
          jobsList.map(async job => ({
            ...job,
            hasAttachments: await checkForAttachments(job.id),
          })),
        )

        setJobs(jobsWithDetails)
        setFilteredJobs(
          jobsWithDetails.sort((a, b) => {
            if (a.createdAt < b.createdAt) return 1
            if (a.createdAt > b.createdAt) return -1
            return 0
          }),
        )
      })

      return unsubscribe
    }

    fetchJobs()
  }, [companyId])

  useEffect(() => {
    if (!companyId) return
    const fetchStatuses = async () => {
      try {
        const statusesRef = doc(db, `companies/${companyId}/settings`, "statuses")
        const statusesDoc = await getDoc(statusesRef)

        if (statusesDoc.exists()) {
          const data = statusesDoc.data()
          setJobStatuses(data.jobs || [])
        }
      } catch (error) {
        console.error("Error fetching statuses:", error)
      }
    }

    fetchStatuses()
  }, [companyId])

  useEffect(() => {
    // Load saved filters from localStorage
    const savedFilters = localStorage.getItem("jobFilters")
    if (savedFilters) {
      setFilters(JSON.parse(savedFilters))
    }
  }, [])

  useEffect(() => {
    const handleClickOutside = event => {
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (!isMouseOverDropdown) {
      const timer = setTimeout(() => {
        setIsStatusDropdownOpen(false)
      }, 200) // Small delay to allow for mouse movement between elements
      return () => clearTimeout(timer)
    }
  }, [isMouseOverDropdown])

  const checkForAttachments = async jobId => {
    const progressUpdatesRef = collection(db, `companies/${companyId}/jobs`, jobId, "progressUpdates")
    const querySnapshot = await getDocs(progressUpdatesRef)
    return querySnapshot.docs.some(doc => doc.data().fileUrl)
  }

  const handleStatusChange = async (jobId, newStatus) => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      const updateData = {
        status: newStatus,
        updatedAt: new Date(),
      }

      // If status is being changed to Completed, set completedAt timestamp
      if (newStatus === "Completed") {
        updateData.completedAt = new Date()
      }

      // If status is being changed to Invoiced, set invoicedAt timestamp
      if (newStatus === "Invoiced") {
        updateData.invoicedAt = new Date()
      }

      await updateDoc(docRef, updateData)
      setJobs(prevJobs => prevJobs.map(job => (job.id === jobId ? { ...job, ...updateData } : job)))
    } catch (error) {
      console.error("Error updating status:", error.message)
    }
  }

  const handleStatusToggle = status => {
    const newStatus = filters.status.includes(status) ? filters.status.filter(s => s !== status) : [...filters.status, status]

    const newFilters = { ...filters, status: newStatus }
    setFilters(newFilters)
    setCurrentPage(1)
    localStorage.setItem("jobFilters", JSON.stringify(newFilters))
  }

  const handleFilterChange = e => {
    const { name, value } = e.target
    let newFilters

    if (name === "status") {
      // Handle multiple select
      const select = e.target
      const selectedOptions = Array.from(select.selectedOptions).map(option => option.value)
      newFilters = { ...filters, [name]: selectedOptions }
    } else {
      newFilters = { ...filters, [name]: value }
    }

    setFilters(newFilters)
    setCurrentPage(1)

    // Save to localStorage
    localStorage.setItem("jobFilters", JSON.stringify(newFilters))
  }

  const clearFilters = () => {
    const newFilters = {
      searchQuery: "",
      status: [],
      location: "All Locations",
      startDate: "",
      endDate: "",
    }
    setFilters(newFilters)
    setCurrentPage(1)
    localStorage.setItem("jobFilters", JSON.stringify(newFilters))
  }

  const applyFiltersAndSorting = () => {
    const { searchQuery, status, location, startDate, endDate } = filters
    let updatedJobs = [...jobs]

    // Apply Filters
    if (searchQuery) {
      updatedJobs = updatedJobs.filter(
        job =>
          job.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          job.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          job.customer?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          job.workOrderId?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (job.displayId &&
            (job.displayId.toLowerCase().includes(searchQuery.toLowerCase()) || job.displayId.split("-").pop().includes(searchQuery))),
      )
    }

    if (status.length > 0) {
      updatedJobs = updatedJobs.filter(job => status.includes(job.status))
    }

    if (location === "Unassigned") {
      updatedJobs = updatedJobs.filter(job => !job.location)
    } else if (location !== "All Locations") {
      updatedJobs = updatedJobs.filter(job => job.location?.toLowerCase() === location.toLowerCase())
    }

    if (startDate) {
      updatedJobs = updatedJobs.filter(job => job.createdAt && new Date(job.createdAt.toDate()) >= new Date(startDate))
    }

    if (endDate) {
      updatedJobs = updatedJobs.filter(job => job.createdAt && new Date(job.createdAt.toDate()) <= new Date(endDate))
    }

    // Apply Sorting
    if (sortConfig.key) {
      updatedJobs.sort((a, b) => {
        let aValue, bValue

        // Handle nested properties (e.g., customer.name)
        if (sortConfig.key.includes(".")) {
          const [parent, child] = sortConfig.key.split(".")
          aValue = a[parent]?.[child] || ""
          bValue = b[parent]?.[child] || ""
        } else {
          // Handle job title (which can be either title or name)
          if (sortConfig.key === "title") {
            aValue = a.title || a.name || ""
            bValue = b.title || b.name || ""
          } else {
            aValue = a[sortConfig.key] || ""
            bValue = b[sortConfig.key] || ""
          }
        }

        // Handle dates specially
        if (sortConfig.key === "createdAt") {
          aValue = aValue?.toDate?.() || new Date(0)
          bValue = bValue?.toDate?.() || new Date(0)
        }

        // Case insensitive comparison for strings
        if (typeof aValue === "string" && typeof bValue === "string") {
          aValue = aValue.toLowerCase()
          bValue = bValue.toLowerCase()
        }

        if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1
        if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1
        return 0
      })
    }

    setFilteredJobs(updatedJobs)
  }

  useEffect(() => {
    applyFiltersAndSorting()
  }, [filters, sortConfig, jobs])

  const requestSort = key => {
    let direction = "ascending"
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending"
    }
    setSortConfig({ key, direction })
  }

  const indexOfLastJob = currentPage * jobsPerPage
  const indexOfFirstJob = indexOfLastJob - jobsPerPage
  const currentJobs = filteredJobs.slice(indexOfFirstJob, indexOfLastJob)
  const totalPages = Math.ceil(filteredJobs.length / jobsPerPage)

  const uniqueLocations = [
    "All Locations",
    ...new Set(
      jobs.reduce((acc, job) => {
        const location = job.location || "Unassigned"
        acc.push(location)
        return acc
      }, []),
    ),
  ]

  const handleMarkCompleted = async jobId => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await updateDoc(docRef, {
        status: "Completed",
        completedAt: new Date(), // Add completion date
      })
      setJobs(prevJobs => prevJobs.map(job => (job.id === jobId ? { ...job, status: "Completed", completedAt: new Date() } : job)))
    } catch (error) {
      console.error("Error marking job as completed:", error.message)
    }
  }

  const handleCloseEmergencyJob = () => {
    setEmergencyModalOpen(false)
  }

  const toggleDropdown = jobId => {
    setDropdownOpen(prev => (prev === jobId ? null : jobId))
  }

  const toggleEmergencyJobModal = (e, type) => {
    e.preventDefault()
    if (emergencyModalOpen) {
      setJobType(null)
    } else {
      setJobType(type)
    }
    setEmergencyModalOpen(prev => !prev)
  }

  const handleAssignClick = jobId => {
    setSelectedJobId(jobId)
    setAssignModalOpen(true)
  }

  const handleCloseAssignModal = () => {
    setAssignModalOpen(false)
    setSelectedJobId(null)
  }

  // Define all available columns
  const availableColumns = [
    { label: "Created", key: "createdAt" },
    { label: "Work Order ID", key: "workOrderId" },
    { label: "ID", key: "displayId" },
    { label: "Priority", key: "priority" },
    { label: "Job Name", key: "title" },
    { label: "Customer", key: "customer.name" },
    { label: "Location", key: "location" },
    { label: "Status", key: "status" },
    { label: "ETA", key: "eta" },
    { label: "Date Completed", key: "completedAt" },
    { label: "Date Invoiced", key: "invoicedAt" },
    { label: "Assigned To", key: "assignedTo" },
    { label: "Subcontractor", key: "subcontractor" },
    { label: "Notes", key: "notes" },
    { label: "Type", key: "type" },
    { label: "Customer Phone", key: "customer.phone" },
    { label: "Customer Email", key: "customer.email" },
    { label: "Site Contact", key: "siteContactName" },
    { label: "Site Contact Phone", key: "siteContactPhone" },
    { label: "Site Contact Email", key: "siteContactEmail" },
  ]

  // Load saved column preferences
  useEffect(() => {
    if (user?.id) {
      const fetchUserPreferences = async () => {
        try {
          const userPrefsRef = doc(db, `companies/${companyId}/users/${user.id}/preferences`, "columns")
          const userPrefsDoc = await getDoc(userPrefsRef)

          if (userPrefsDoc.exists()) {
            const { visibleColumns, columnOrder } = userPrefsDoc.data()
            setVisibleColumns(visibleColumns)
            // Reorder availableColumns based on saved order
            const orderedAvailableColumns = [...availableColumns].sort((a, b) => columnOrder.indexOf(a.key) - columnOrder.indexOf(b.key))
            setOrderedColumns(orderedAvailableColumns)
            dispatch(setColumnPreferences({ visibleColumns, columnOrder }))
          } else {
            // Default columns if none saved
            const defaultColumns = [
              "createdAt",
              "workOrderId",
              "displayId",
              "priority",
              "title",
              "customer.name",
              "location",
              "status",
              "completedAt",
              "invoicedAt",
            ]
            setVisibleColumns(defaultColumns)
            setOrderedColumns(availableColumns)
            dispatch(setColumnPreferences({ visibleColumns: defaultColumns, columnOrder: availableColumns.map(col => col.key) }))
          }
        } catch (error) {
          console.error("Error fetching user preferences:", error)
        }
      }

      fetchUserPreferences()
    }
  }, [user?.id, companyId])

  // Save column preferences
  const handleSaveColumns = async (columns, newOrder) => {
    setVisibleColumns(columns)
    setOrderedColumns(newOrder)
    if (user?.id) {
      try {
        const userPrefsRef = doc(db, `companies/${companyId}/users/${user.id}/preferences`, "columns")
        await setDoc(userPrefsRef, {
          visibleColumns: columns,
          columnOrder: newOrder.map(col => col.key),
        })
        dispatch(setColumnPreferences({ visibleColumns: columns, columnOrder: newOrder.map(col => col.key) }))
      } catch (error) {
        console.error("Error saving column preferences:", error)
      }
    }
  }

  // Get ordered columns based on visible columns
  const getOrderedColumns = () => {
    return orderedColumns.filter(column => visibleColumns.includes(column.key))
  }

  const handleSubcontractorSelect = async subcontractor => {
    if (!selectedJobForSubcontractor) return

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, selectedJobForSubcontractor)
      await updateDoc(docRef, {
        subcontractor: subcontractor,
        updatedAt: new Date(),
      })

      setJobs(prevJobs => prevJobs.map(job => (job.id === selectedJobForSubcontractor ? { ...job, subcontractor: subcontractor } : job)))
    } catch (error) {
      console.error("Error updating subcontractor:", error)
    } finally {
      setSubcontractorModalOpen(false)
      setSelectedJobForSubcontractor(null)
    }
  }

  return (
    <>
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold mb-4 text-gray-900">Jobs</h1>
          <div className="flex gap-10">
            <button
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded shadow flex items-center gap-2"
              onClick={() => setIsColumnSelectorOpen(true)}
            >
              <FaSort size={16} /> Columns
            </button>
            <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded shadow" onClick={toggleEmergencyJobModal}>
              + New Job
            </button>
            <button
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded shadow flex items-center gap-2"
              onClick={e => toggleEmergencyJobModal(e, "Emergency")}
            >
              + <FaExclamationTriangle size={16} /> Job
            </button>
          </div>
        </div>
        <div className="mb-6 bg-white p-4 rounded shadow grid grid-cols-1 md:grid-cols-12 gap-4">
          <div className="md:col-span-6 flex gap-4">
            <input
              type="text"
              name="searchQuery"
              placeholder="Search Job Name, ID, Customer, or Work Order ID"
              value={filters.searchQuery}
              onChange={handleFilterChange}
              className="border border-gray-300 rounded p-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 h-9 flex-1"
            />
            <select
              value={resultsPerPage}
              onChange={e => {
                setResultsPerPage(Number(e.target.value))
                setCurrentPage(1)
              }}
              className="border border-gray-300 rounded p-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 w-32 h-9"
            >
              <option value={10}>10 per page</option>
              <option value={20}>20 per page</option>
              <option value={50}>50 per page</option>
            </select>
          </div>
          <div className="md:col-span-4">
            <select
              name="location"
              value={filters.location}
              onChange={handleFilterChange}
              className="border border-gray-300 rounded p-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 w-full h-9"
            >
              {uniqueLocations.map((location, index) => (
                <option key={location + index} value={location}>
                  {location}
                </option>
              ))}
            </select>
          </div>
          <div className="md:col-span-2">
            <div className="flex flex-col gap-2">
              {filters.status.length > 0 && (
                <button onClick={clearFilters} className="text-sm text-blue-600 hover:text-blue-800">
                  Clear Filters
                </button>
              )}
              <div
                className="relative"
                ref={statusDropdownRef}
                onMouseEnter={() => setIsMouseOverDropdown(true)}
                onMouseLeave={() => setIsMouseOverDropdown(false)}
              >
                <button
                  onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
                  className="w-full border border-gray-300 rounded p-2 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                >
                  {filters.status.length > 0 ? `${filters.status.length} status${filters.status.length > 1 ? "es" : ""} selected` : "Select Statuses"}
                </button>
                {isStatusDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
                    <div className="max-h-60 overflow-y-auto">
                      {jobStatuses.map(status => (
                        <label key={status} className="flex items-center px-4 py-2 hover:bg-gray-100 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={filters.status.includes(status)}
                            onChange={() => handleStatusToggle(status)}
                            className="mr-2"
                          />
                          <span className="text-gray-900">{status}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              {filters.status.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {filters.status.map(selectedStatus => (
                    <span
                      key={selectedStatus}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {selectedStatus}
                      <button onClick={() => handleStatusToggle(selectedStatus)} className="ml-1 text-blue-600 hover:text-blue-800">
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* <div className="bg-white p-4 rounded shadow"> */}
        <div className=" bg-white rounded-lg p-4 mx-auto shadow-md">
          {currentJobs.length > 0 ? (
            <>
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    {getOrderedColumns().map(column => (
                      <th key={column.key} className="border p-2 text-gray-900 cursor-pointer" onClick={() => requestSort(column.key)}>
                        <div className="flex items-center justify-between">
                          {column.label}
                          {sortConfig.key === column.key ? sortConfig.direction === "ascending" ? <FaSortUp /> : <FaSortDown /> : <FaSort />}
                        </div>
                      </th>
                    ))}
                    <th className="border p-2 text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {currentJobs.map(job => (
                    <tr key={job.id} className={`${job.type === "Emergency" ? "bg-red-200" : "hover:bg-gray-50"}`}>
                      {getOrderedColumns().map(column => {
                        let cellContent = "N/A"

                        if (column.key === "createdAt") {
                          cellContent = job.createdAt ? dayjs(job.createdAt.toDate()).format("M/D/YY") : "N/A"
                        } else if (column.key === "workOrderId") {
                          cellContent = job.workOrderId
                        } else if (column.key === "displayId") {
                          cellContent = (
                            <a onClick={() => router.push(`/jobs/${job.id}`)} className="underline text-blue-600 hover:text-blue-800 cursor-pointer">
                              {job.displayId}
                            </a>
                          )
                        } else if (column.key === "priority") {
                          cellContent = (
                            <PriorityPicker
                              jobId={job.id}
                              currentPriority={job.priority}
                              onPriorityChange={newPriority => {
                                setJobs(prevJobs => prevJobs.map(j => (j.id === job.id ? { ...j, priority: newPriority } : j)))
                              }}
                            />
                          )
                        } else if (column.key === "title") {
                          cellContent = job.title || job.name
                        } else if (column.key === "customer.name") {
                          cellContent = job.customer?.name
                        } else if (column.key === "location") {
                          cellContent = job.location
                        } else if (column.key === "status") {
                          cellContent = (
                            <StatusPickerWithColor
                              type="jobs"
                              currentStatus={job?.status}
                              onStatusChange={newStatus => handleStatusChange(job.id, newStatus)}
                              className="max-w-xs"
                            />
                          )
                        } else if (column.key === "eta") {
                          cellContent = (
                            <div className="flex items-center gap-2">
                              {job.eta ? (
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={() => {
                                      setSelectedJobForETA(job.id)
                                      setEtaModalOpen(true)
                                    }}
                                    className="text-gray-900 hover:text-blue-500 text-left"
                                  >
                                    <div>
                                      {dayjs(job.eta?.toDate?.() || job.eta)
                                        .tz(getTimezoneFromLocation(job.location))
                                        .format("M/D/YY")}
                                    </div>
                                    <div>
                                      {dayjs(job.eta?.toDate?.() || job.eta)
                                        .tz(getTimezoneFromLocation(job.location))
                                        .format("h:mm")}
                                      {dayjs(job.eta?.toDate?.() || job.eta)
                                        .tz(getTimezoneFromLocation(job.location))
                                        .format("a")
                                        .toLowerCase()}{" "}
                                      {getTimezoneAbbr(getTimezoneFromLocation(job.location))}
                                    </div>
                                  </button>
                                </div>
                              ) : (
                                <button
                                  onClick={() => {
                                    setSelectedJobForETA(job.id)
                                    setEtaModalOpen(true)
                                  }}
                                  className="text-blue-500 hover:text-blue-700"
                                >
                                  + Set ETA
                                </button>
                              )}
                            </div>
                          )
                        } else if (column.key === "completedAt") {
                          cellContent = job.completedAt
                            ? typeof job.completedAt === "object" && job.completedAt.toDate
                              ? dayjs(job.completedAt.toDate()).format("M/D/YY")
                              : dayjs(job.completedAt).format("M/D/YY")
                            : "N/A"
                        } else if (column.key === "invoicedAt") {
                          cellContent = job.invoicedAt
                            ? typeof job.invoicedAt === "object" && job.invoicedAt.toDate
                              ? dayjs(job.invoicedAt.toDate()).format("M/D/YY")
                              : dayjs(job.invoicedAt).format("M/D/YY")
                            : "N/A"
                        } else if (column.key === "assignedTo") {
                          cellContent = job.assignedTo?.name || "Unassigned"
                        } else if (column.key === "subcontractor") {
                          cellContent = (
                            <div className="flex items-center w-full">
                              {job.subcontractor ? (
                                <div className="flex items-center w-full">
                                  <div className="flex-1">
                                    <Tooltip.Root>
                                      <Tooltip.Trigger asChild>
                                        <button
                                          onClick={() => {
                                            setSelectedJobForSubcontractor(job.id)
                                            setSubcontractorModalOpen(true)
                                          }}
                                          className="text-gray-900 hover:text-blue-500 text-left"
                                        >
                                          {job.subcontractor.name}
                                        </button>
                                      </Tooltip.Trigger>
                                      <Tooltip.Content className="bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg">
                                        <div className="flex flex-col gap-1">
                                          <div>{job.subcontractor.name}</div>
                                          {job.subcontractor.phone && <div>{job.subcontractor.phone}</div>}
                                          {job.subcontractor.email && <div>{job.subcontractor.email}</div>}
                                        </div>
                                      </Tooltip.Content>
                                    </Tooltip.Root>
                                  </div>
                                  <button
                                    onClick={async () => {
                                      try {
                                        const docRef = doc(db, `companies/${companyId}/jobs`, job.id)
                                        await updateDoc(docRef, {
                                          subcontractor: deleteField(),
                                          updatedAt: new Date(),
                                        })
                                        setJobs(prevJobs => prevJobs.map(j => (j.id === job.id ? { ...j, subcontractor: null } : j)))
                                      } catch (error) {
                                        console.error("Error removing subcontractor:", error)
                                      }
                                    }}
                                    className="text-red-500 hover:text-red-700 ml-2"
                                  >
                                    <FaTimes size={14} />
                                  </button>
                                </div>
                              ) : (
                                <button
                                  onClick={() => {
                                    setSelectedJobForSubcontractor(job.id)
                                    setSubcontractorModalOpen(true)
                                  }}
                                  className="text-blue-500 hover:text-blue-700"
                                >
                                  + Add Subcontractor
                                </button>
                              )}
                            </div>
                          )
                        } else if (column.key === "notes") {
                          cellContent = (
                            <div className="relative">
                              {job.notes && job.notes.length > 0 ? (
                                <div className="flex items-center gap-2">
                                  <span className="text-gray-600">
                                    {job.notes.length} note{job.notes.length !== 1 ? "s" : ""}
                                  </span>
                                  <button
                                    className="text-blue-500 hover:text-blue-700"
                                    onClick={() => setHoveredJobIdForNotes(hoveredJobIdForNotes === job.id ? null : job.id)}
                                  >
                                    <FaStickyNote size={16} />
                                  </button>
                                </div>
                              ) : (
                                <span className="text-gray-500">No notes</span>
                              )}
                              <NotesPopup
                                notes={job.notes}
                                isOpen={hoveredJobIdForNotes === job.id}
                                jobId={job.id}
                                companyId={companyId}
                                onMouseLeave={() => setHoveredJobIdForNotes(null)}
                              />
                            </div>
                          )
                        } else if (column.key === "type") {
                          cellContent = job.type || "Standard"
                        } else if (column.key === "customer.phone") {
                          cellContent = job.customer?.phone || "N/A"
                        } else if (column.key === "customer.email") {
                          cellContent = job.customer?.email || "N/A"
                        } else if (column.key === "siteContactName") {
                          cellContent = job.siteContactName || "N/A"
                        } else if (column.key === "siteContactPhone") {
                          cellContent = job.siteContactPhone || "N/A"
                        } else if (column.key === "siteContactEmail") {
                          cellContent = job.siteContactEmail || "N/A"
                        }

                        return (
                          <td key={column.key} className="border p-2 text-gray-800">
                            {cellContent}
                          </td>
                        )
                      })}
                      <td className="border p-2 relative">
                        <div className="flex items-center justify-around gap-2">
                          {/* View Button */}
                          <div className="relative">
                            <Tooltip.Root>
                              <Tooltip.Trigger asChild>
                                <button className="text-blue-500 hover:text-blue-700" onClick={() => router.push(`/jobs/${job.id}`)}>
                                  <FaEye size={16} />
                                </button>
                              </Tooltip.Trigger>
                              <Tooltip.Content className="bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg">View Job</Tooltip.Content>
                            </Tooltip.Root>
                          </div>

                          {/* Notes Button */}
                          {job.notes && job.notes.length > 0 && (
                            <div className="relative">
                              <Tooltip.Root>
                                <Tooltip.Trigger asChild>
                                  <button
                                    className="text-orange-500 hover:text-orange-700"
                                    onClick={() => setHoveredJobIdForNotes(hoveredJobIdForNotes === job.id ? null : job.id)}
                                  >
                                    <FaStickyNote size={16} />
                                  </button>
                                </Tooltip.Trigger>
                                <Tooltip.Content
                                  className="bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg max-w-xs z-50"
                                  side="left"
                                  align="center"
                                >
                                  {typeof job.notes[0] === "string" ? job.notes[0] : job.notes[0]?.text || "No note text"}
                                </Tooltip.Content>
                              </Tooltip.Root>
                              <NotesPopup
                                notes={job.notes}
                                isOpen={hoveredJobIdForNotes === job.id}
                                jobId={job.id}
                                companyId={companyId}
                                onMouseLeave={() => setHoveredJobIdForNotes(null)}
                              />
                            </div>
                          )}

                          {/* Comments Button */}
                          <div className="relative">
                            <Tooltip.Root>
                              <Tooltip.Trigger asChild>
                                <button
                                  className="text-blue-500 hover:text-blue-700"
                                  onClick={() => setHoveredJobId(hoveredJobId === job.id ? null : job.id)}
                                >
                                  <FaComment size={16} />
                                </button>
                              </Tooltip.Trigger>
                              <Tooltip.Content
                                className="bg-gray-800 text-white text-sm px-2 py-1 rounded shadow-lg max-w-xs z-50"
                                side="left"
                                align="center"
                              >
                                Click to view comments
                              </Tooltip.Content>
                            </Tooltip.Root>
                            <CommentsPopup
                              comments={job.comments}
                              isOpen={hoveredJobId === job.id}
                              jobId={job.id}
                              companyId={companyId}
                              onMouseLeave={() => setHoveredJobId(null)}
                            />
                          </div>

                          {/* Slack Integration */}
                          {/* <SlackIntegration
                            jobId={job.id}
                            jobName={job.title || job.name}
                            onError={error => {
                              // You can handle errors here, e.g., show a toast notification
                              console.error("Slack integration error:", error)
                            }}
                          /> */}

                          {/* Actions Dropdown */}
                          <Tooltip.Root>
                            <Tooltip.Trigger asChild>
                              <div className="relative" ref={dropdownRef}>
                                <button className="text-gray-500 hover:text-gray-700" onClick={() => toggleDropdown(job.id)} title="Actions">
                                  <AiOutlineEllipsis size={20} />
                                </button>
                                {dropdownOpen === job.id && (
                                  <div className="absolute right-0 mt-2 bg-white border border-gray-300 rounded shadow-lg z-10 dropdown-content">
                                    <ul className="py-2 text-sm text-gray-700">
                                      <li>
                                        <button
                                          className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                                          onClick={() => {
                                            toggleDropdown(job.id)
                                            handleMarkCompleted(job.id)
                                          }}
                                        >
                                          <FaCheckCircle size={16} className="inline mr-2 text-yellow-500" />
                                          Mark Completed
                                        </button>
                                      </li>
                                      <li>
                                        <button
                                          className="block w-full text-left px-4 py-2 hover:bg-gray-100"
                                          onClick={() => {
                                            toggleDropdown(job.id)
                                            handleAssignClick(job.id)
                                          }}
                                        >
                                          <FaPersonCirclePlus size={16} className="inline mr-2 text-purple-500" />
                                          Assign
                                        </button>
                                      </li>
                                      <li>
                                        <button
                                          className="block w-full text-left px-4 py-2 text-red-500 hover:bg-gray-100"
                                          onClick={async () => {
                                            toggleDropdown(job.id)
                                            const confirmDelete = window.confirm(
                                              `Are you sure you want to delete job ${job.displayId} - "${
                                                job.title || job.name
                                              }"? This action cannot be undone.`,
                                            )
                                            if (!confirmDelete) return

                                            try {
                                              const docRef = doc(db, `companies/${companyId}/jobs`, job.id)
                                              console.log("Deleting job:", job.id, docRef)
                                              await deleteDoc(docRef)
                                            } catch (err) {
                                              console.error("Error deleting job:", err.message)
                                            }
                                          }}
                                        >
                                          <FaTrashAlt size={16} className="inline mr-2 text-red-500" />
                                          Delete
                                        </button>
                                      </li>
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </Tooltip.Trigger>
                            <Tooltip.Content className="bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg">More</Tooltip.Content>
                          </Tooltip.Root>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              <div className="flex justify-between items-center mt-4">
                {currentPage > 1 && (
                  <button
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  >
                    Previous
                  </button>
                )}
                <p className="text-gray-700">
                  Page {currentPage} of {totalPages}
                </p>
                {currentPage < totalPages && (
                  <button
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  >
                    Next
                  </button>
                )}
              </div>
            </>
          ) : (
            <p className="text-gray-600">No jobs found. Try adjusting your filters.</p>
          )}
        </div>
      </div>
      {assignModalOpen && selectedJobId && jobs.length > 0 && (
        <AssignJobModal
          isOpen={assignModalOpen}
          onClose={handleCloseAssignModal}
          jobId={selectedJobId}
          jobName={jobs.find(job => job.id === selectedJobId)?.title || jobs.find(job => job.id === selectedJobId)?.name}
          jobDisplayId={jobs.find(job => job.id === selectedJobId)?.displayId}
        />
      )}
      <EmergencyJobModal isOpen={emergencyModalOpen} onClose={handleCloseEmergencyJob} type={jobType} />
      <ColumnSelectorModal
        isOpen={isColumnSelectorOpen}
        onClose={() => setIsColumnSelectorOpen(false)}
        availableColumns={orderedColumns}
        visibleColumns={visibleColumns}
        onSave={handleSaveColumns}
      />
      <SubcontractorSelectionModal
        isOpen={subcontractorModalOpen}
        onClose={() => {
          setSubcontractorModalOpen(false)
          setSelectedJobForSubcontractor(null)
        }}
        onSelect={handleSubcontractorSelect}
        currentSubcontractor={selectedJobForSubcontractor ? jobs.find(j => j.id === selectedJobForSubcontractor)?.subcontractor : null}
      />
      <ETAPickerModal
        isOpen={etaModalOpen}
        onClose={() => {
          setEtaModalOpen(false)
          setSelectedJobForETA(null)
        }}
        jobId={selectedJobForETA}
        currentETA={selectedJobForETA ? jobs.find(j => j.id === selectedJobForETA)?.eta : null}
        onETAChange={newETA => {
          setJobs(prevJobs => prevJobs.map(j => (j.id === selectedJobForETA ? { ...j, eta: newETA } : j)))
        }}
      />
    </>
  )
}
