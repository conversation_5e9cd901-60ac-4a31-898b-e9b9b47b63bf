"use client"

import React, { useEffect, useState, useRef } from "react"
import { useRouter, useParams } from "next/navigation"
import { doc, getDoc, updateDoc, collection, addDoc, getDocs, deleteDoc, setDoc, onSnapshot, deleteField, Timestamp } from "firebase/firestore"
import { deleteObject, ref, uploadBytes, getDownloadURL } from "firebase/storage"
import ProgressUpdatesModal from "@/components/modals/ProgressUpdatesModal"
import JobProgress from "@/components/JobProgress"
import { db, storage } from "@/firebase"
import { useSelector } from "react-redux"
import Divider from "@/components/Divider"
import { formatPhone } from "@/utils/formatPhone"
import dayjs from "dayjs"
import JobServicesTable from "@/components/JobServicesTable"
import ApprovalComponent from "@/components/ApprovalComponent"
import StatusPickerWithColor from "@/components/StatusPickerWithColor"
import { HiChevronDown, Hi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Hi<PERSON> } from "react-icons/hi"
import { IoArrowBackOutline } from "react-icons/io5"
import { FaTrashAlt, FaEye, FaEdit, FaCheck, FaPlus, FaFileInvoice, FaTimes, FaEnvelope } from "react-icons/fa"
import { FaPersonCirclePlus } from "react-icons/fa6"
import { useUser } from "@/hooks/useUser"
import { useCSAs } from "@/hooks/useCSAs"
import { useInvoices } from "@/hooks/useInvoices"
import RateItemSelector from "@/components/RateItemSelector"
import AddressAutocomplete from "@/components/AddressAutocomplete"
import DatePicker from "@/components/DatePicker"
import BackButton from "@/components/BackButton"
import { getDueDateClass } from "@/utils/getDueDateClass"
import { sortedById } from "@/utils/sortedById"
import CommentsSection from "@/components/CommentsSection"
import NotesSection from "@/components/NotesSection"
import CopyOrderButton from "@/components/CopyOrderButton"
import ChangeOrderModal from "@/components/modals/ChangeOrderModal"
import ChangeOrdersManager from "@/components/ChangeOrdersManager"
import ServiceSelector from "@/components/ServiceSelector"
import { getOrderTypeFromId } from "@/utils/getOrderTypeFromId"
import ServicePicker from "@/components/ServicePicker"
import { generateManagerSignoffPDF } from "@/utils/generateManagerSignoffPDF"
import UserAssignmentModal from "@/components/modals/UserAssignmentModal"
import { notifyJobAssignment } from "@/utils/notifications"
import SubcontractorSelectionModal from "@/components/modals/SubcontractorSelectionModal"
import * as Tooltip from "@radix-ui/react-tooltip"
import ETAPicker from "@/components/ETAPicker"
import { getTimezoneFromLocation, getTimezoneAbbr } from "@/utils/locationToTimezone"
import ETAPickerModal from "@/components/modals/ETAPickerModal"
import EmailCompositionModal from "@/components/modals/EmailCompositionModal"
import { useQuotes } from "@/hooks/useQuotes"
// import SlackChat from "@/components/SlackChat"

export default function JobDetails() {
  const params = useParams()
  const id = params.id // Extract job ID from the route
  const [job, setJob] = useState(null)
  const [newAttachment, setNewAttachment] = useState(null)
  const [attachments, setAttachments] = useState([])
  const [deletingAttachmentId, setDeletingAttachmentId] = useState(null)
  const [services, setServices] = useState([]) // State to store services
  const [customers, setCustomers] = useState([])
  const [rate, setRate] = useState(null)
  const [currentAttachment, setCurrentAttachment] = useState(null)
  const [loading, setLoading] = useState(false)
  const [dragging, setDragging] = useState(false)
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef(null)
  const [subcontractors, setSubcontractors] = useState([])
  const [progressUpdates, setProgressUpdates] = useState([])
  const [isModalOpen, setIsModalOpen] = useState(false) // State for progress updates modal
  const [error, setError] = useState("")
  const router = useRouter()
  const { companyId, companyName } = useSelector(state => state.company)
  const [globalSubcontractor, setGlobalSubcontractor] = useState(null)
  const [isQuotesSectionExpanded, setIsQuotesSectionExpanded] = useState(false)
  const [isCSASectionExpanded, setIsCSASectionExpanded] = useState(false)
  const [isInvoiceSectionExpanded, setIsInvoiceSectionExpanded] = useState(false)
  const { admin } = useUser()
  const [editableJob, setEditableJob] = useState(null)
  const [isEditing, setIsEditing] = useState(false)
  const { quotes, deleteQuote } = useQuotes("job", companyId, id)
  const { csas, deleteCSA } = useCSAs(companyId, id)
  const { invoices, deleteInvoice } = useInvoices(companyId, id)
  const [contacts, setContacts] = useState([])
  const [filteredCustomers, setFilteredCustomers] = useState([])
  const [filteredContacts, setFilteredContacts] = useState([])
  const [termTypes, setTermTypes] = useState([])
  const [isChangeOrderOpen, setIsChangeOrderOpen] = useState(false)
  const [isUserAssignmentModalVisible, setIsUserAssignmentModalVisible] = useState(false)
  const [assignedUser, setAssignedUser] = useState(null)
  const [subcontractorModalOpen, setSubcontractorModalOpen] = useState(false)
  const [etaModalOpen, setEtaModalOpen] = useState(false)
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false)
  const [emailModalRecipient, setEmailModalRecipient] = useState("")
  const [previewAttachment, setPreviewAttachment] = useState(null)
  const [isPdfPreview, setIsPdfPreview] = useState(false)
  const [changeOrders, setChangeOrders] = useState([])

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset editableJob to original data if editing is canceled
      setEditableJob(job)
    }
    setIsEditing(!isEditing)
  }

  const handleFieldChange = (field, value) => {
    setEditableJob(prev => ({ ...prev, [field]: value }))
  }

  const handleGlobalSubcontractorChange = async e => {
    const selected = subcontractors.find(sub => sub.id === e.target.value)

    if (selected) {
      if (!confirm(`Are you sure you want to assign ${selected.name} to all services?`)) {
        return
      }

      try {
        const docRef = doc(db, `companies/${companyId}/jobs`, id)
        await updateDoc(docRef, {
          subcontractor: selected,
          updatedAt: new Date(),
        })
        setGlobalSubcontractor(selected)
      } catch (error) {
        console.error("Error updating job subcontractor:", error)
        alert("Failed to update subcontractor. Please try again.")
      }
    } else {
      try {
        const docRef = doc(db, `companies/${companyId}/jobs`, id)
        await updateDoc(docRef, {
          subcontractor: deleteField(),
          updatedAt: new Date(),
        })
        setGlobalSubcontractor(null)
      } catch (error) {
        console.error("Error removing subcontractor:", error)
        alert("Failed to remove subcontractor. Please try again.")
      }
    }
  }

  const handleSave = async () => {
    if (!companyId || !editableJob) return
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, id)
      await updateDoc(docRef, { ...editableJob })
      setIsEditing(false) // Exit editing mode
    } catch (error) {
      console.error("Error saving job details:", error.message)
    }
  }

  const handleSaveChangeOrder = async changeOrder => {
    // Convert each service's cost and sell fields into numbers rounded to two decimals.
    const newServices = changeOrder.map(service => ({
      ...service,
      cost: service.cost === "" ? 0 : Math.round(Number(service.cost) * 100) / 100,
      sell: service.sell === "" ? 0 : Math.round(Number(service.sell) * 100) / 100,
    }))

    try {
      // Get a reference to the job document.
      const jobDocRef = doc(db, `companies/${companyId}/jobs`, id)
      // Create a reference for a new document in the "changeOrders" subcollection.
      const changeOrdersCollectionRef = collection(jobDocRef, "changeOrders")
      const newDocRef = doc(changeOrdersCollectionRef)

      // Build the new change order object using the Firestore-generated ID.
      const newChangeOrder = {
        id: newDocRef.id,
        createdAt: new Date(),
        services: newServices,
      }

      // Save the new change order document.
      await setDoc(newDocRef, newChangeOrder)
      setIsChangeOrderOpen(false)
    } catch (error) {
      console.error("Error saving job change order:", error.message)
    }
  }

  const fetchAttachments = () => {
    if (!companyId || !id) return

    const attachmentsRef = collection(db, `companies/${companyId}/jobs/${id}/attachments`)

    const unsubscribe = onSnapshot(
      attachmentsRef,
      snapshot => {
        const attachmentsList = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
        setAttachments(attachmentsList)
      },
      err => {
        console.error("Error fetching attachments:", err.message)
      },
    )

    // Return the unsubscribe function to clean up the listener
    return unsubscribe
  }

  const fetchJob = async () => {
    if (!companyId || !id) return

    const docRef = doc(db, `companies/${companyId}/jobs`, id)
    const unsubscribe = onSnapshot(
      docRef,
      async docSnap => {
        if (docSnap.exists()) {
          const jobData = docSnap.data()
          setJob(jobData)
          setEditableJob(jobData)
          setGlobalSubcontractor(jobData.subcontractor)

          // Fetch and set assigned user if exists
          if (jobData.assignedTo) {
            const userRef = doc(db, `companies/${companyId}/users`, jobData.assignedTo)
            const userSnap = await getDoc(userRef)
            if (userSnap.exists()) {
              setAssignedUser({ id: userSnap.id, ...userSnap.data() })
            }
          } else {
            setAssignedUser(null)
          }
        }
      },
      err => {
        console.error("Error fetching job:", err)
      },
    )

    return () => unsubscribe()
  }

  useEffect(() => {
    if (!companyId || !id) return

    const fetchSubcontractors = async () => {
      try {
        const subcontractorsRef = collection(db, `companies/${companyId}/subcontractors`)
        const querySnapshot = await getDocs(subcontractorsRef)
        const fetchedSubcontractors = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))
        setSubcontractors(fetchedSubcontractors)
      } catch (err) {
        console.error("Error fetching subcontractors:", err.message)
      }
    }

    const fetchServices = async () => {
      try {
        const servicesRef = collection(db, `companies/${companyId}/services`)
        const snapshot = await getDocs(servicesRef)
        const servicesList = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
        setServices(servicesList)
      } catch (err) {
        console.error("Error fetching services:", err.message)
      }
    }

    const fetchChangeOrders = async () => {
      try {
        const changeOrdersRef = collection(db, `companies/${companyId}/jobs/${id}/changeOrders`)
        const snapshot = await getDocs(changeOrdersRef)
        const changeOrdersList = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
        setChangeOrders(changeOrdersList)
      } catch (err) {
        console.error("Error fetching change orders:", err.message)
      }
    }

    fetchJob()
    const unsubscribeAttachments = fetchAttachments?.() // Ensure `fetchAttachments` is returning an unsubscribe

    fetchServices()
    fetchSubcontractors()
    fetchChangeOrders()

    // Cleanup the listeners on unmount
    return () => {
      if (unsubscribeAttachments) unsubscribeAttachments()
    }
  }, [companyId, id])

  useEffect(() => {
    const fetchCustomers = async () => {
      if (!companyId) return
      const querySnapshot = await getDocs(collection(db, `companies/${companyId}/customers`))
      const customerList = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
      setCustomers(customerList)
    }

    const fetchContacts = async () => {
      if (!companyId) return
      const querySnapshot = await getDocs(collection(db, `companies/${companyId}/contacts`))
      const contactList = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
      setContacts(contactList)
    }

    fetchCustomers()
    fetchContacts()
  }, [companyId])

  const handleDelete = async () => {
    if (
      !confirm(
        `Are you sure you want to delete job ${editableJob.displayId} - "${editableJob.title || editableJob.name}"? This action cannot be undone.`,
      )
    )
      return

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, id)
      await deleteDoc(docRef)
      router.push("/jobs")
    } catch (err) {
      console.error("Error deleting job:", err.message)
      setError("Failed to delete job. Please try again.")
    }
  }

  const handleChange = e => {
    const { name, value } = e.target

    setEditableJob(prev => {
      if (name.startsWith("customer.")) {
        // If the field is part of the customer object, update it correctly
        const customerField = name.split(".")[1]
        return {
          ...prev,
          customer: {
            ...prev.customer,
            [customerField]: value,
          },
        }
      }

      if (name === "nte") {
        return {
          ...prev,
          nte: Number(value),
        }
      }

      // Update other form fields
      return {
        ...prev,
        [name]: value,
      }
    })

    // Handle autocomplete filtering for customer name
    if (name === "customer.name") {
      const matches = customers.filter(customer => customer.name.toLowerCase().includes(value.toLowerCase()))
      setFilteredCustomers(matches)
    }
    if (name === "siteContactName") {
      const matches = contacts.filter(contact => contact.name.toLowerCase().includes(value.toLowerCase()))
      setFilteredContacts(matches)
    }
  }

  const handleStatusChange = async (jobId, newStatus) => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      const updateData = {
        status: newStatus,
        updatedAt: new Date(),
      }

      // If status is being changed to Completed, set completedAt timestamp
      if (newStatus === "Completed") {
        updateData.completedAt = new Date()
      }

      // If status is being changed to Invoiced, set invoicedAt timestamp
      if (newStatus === "Invoiced") {
        updateData.invoicedAt = new Date()
      }

      await updateDoc(docRef, updateData)

      setEditableJob(prevJob => ({
        ...prevJob,
        ...updateData,
        completedAt: updateData.completedAt ? new Timestamp(updateData.completedAt.getTime() / 1000, 0) : null,
        invoicedAt: updateData.invoicedAt ? new Timestamp(updateData.invoicedAt.getTime() / 1000, 0) : null,
      }))
    } catch (error) {
      console.error("Error updating status:", error.message)
    }
  }

  const handleRemoveAttachment = async attachmentId => {
    try {
      // Fetch the attachment details
      const path = `companies/${companyId}/jobs/${id}/attachments`
      const attachmentRef = doc(db, path, attachmentId)
      const attachmentDoc = await getDoc(attachmentRef)

      if (!attachmentDoc.exists()) {
        console.error("Attachment not found.")
        return
      }

      const attachmentData = attachmentDoc.data()
      const fileUrl = attachmentData.url

      // Log the file URL
      console.log("File URL:", fileUrl)

      // Extract the file path from the URL
      const storageUrlPattern = /\/o\/([^?]+)/
      const match = fileUrl.match(storageUrlPattern)

      if (!match || !match[1]) {
        throw new Error(`Failed to parse the file path from URL: ${fileUrl}`)
      }

      const filePath = decodeURIComponent(match[1])

      // Log the extracted file path
      console.log("Extracted file path:", filePath)

      // Delete the file from Firebase Storage
      const fileRef = ref(storage, filePath)
      await deleteObject(fileRef)

      // Delete the attachment record from Firestore
      await deleteDoc(attachmentRef)

      // Update local state
      // setAttachments(prev => prev.filter(att => att.id !== attachmentId))
      console.log("Attachment successfully deleted.")
    } catch (err) {
      console.error("Error removing attachment:", err.message)
    }
  }

  const handleFileUpload = async files => {
    if (!files.length) return

    setUploading(true) // Set uploading to true during the process
    try {
      const attachmentsRef = collection(db, `companies/${companyId}/jobs/${id}/attachments`)

      const uploadedFiles = await Promise.all(
        Array.from(files).map(async file => {
          const storageRef = ref(storage, `companies/${companyId}/jobs/${id}/attachments/${file.name}`)
          await uploadBytes(storageRef, file)
          const downloadURL = await getDownloadURL(storageRef)

          const docRef = await addDoc(attachmentsRef, {
            name: file.name,
            url: downloadURL,
          })
          return { id: docRef.id, name: file.name, url: downloadURL }
        }),
      )

      // setAttachments(prev => [...prev, ...uploadedFiles])
    } catch (err) {
      console.error("Error uploading files:", err.message)
    } finally {
      setUploading(false) // Reset uploading state after process completion
    }
  }

  const handleDragOver = e => {
    e.preventDefault()
    setDragging(true) // Set dragging state to true
  }

  const handleDragLeave = () => {
    setDragging(false) // Reset dragging state
  }

  const handleDrop = e => {
    e.preventDefault()
    setDragging(false) // Reset dragging state
    const files = e.dataTransfer.files
    handleFileUpload(files)
  }

  const handleFileSelect = e => {
    const files = e.target.files
    handleFileUpload(files)
  }

  const handleCustomerSelect = customer => {
    setEditableJob(prev => ({
      ...prev,
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email || "",
        phone: customer.phone || "",
      },
    }))
    setFilteredCustomers([])
  }

  const handleContactSelect = contact => {
    setEditableJob(prev => ({
      ...prev,
      siteContactName: contact.name,
      siteContactEmail: contact.email || "",
      siteContactPhone: contact.phone || "",
      contact,
    }))
    setFilteredContacts([])
  }

  const handleAddExistingService = async selectedService => {
    if (!selectedService) {
      console.error("No service selected")
      return
    }

    setLoading(true)
    try {
      // Check if service already exists
      const existingServiceIndex = job.services?.findIndex(s => s.id === selectedService.id)
      let updatedServices = []

      if (existingServiceIndex >= 0) {
        // If service exists, increment quantity
        updatedServices = [...(job.services || [])]
        updatedServices[existingServiceIndex] = {
          ...updatedServices[existingServiceIndex],
          quantity: (Number(updatedServices[existingServiceIndex].quantity) || 1) + 1,
        }
      } else {
        // If service doesn't exist, add it with quantity 1
        updatedServices = [
          ...(job.services || []),
          {
            ...selectedService,
            quantity: 1,
          },
        ]
      }

      const docRef = doc(db, `companies/${companyId}/jobs`, id)
      await updateDoc(docRef, { services: updatedServices })

      // The job will automatically update through the Firestore listener
    } catch (err) {
      console.error("Error updating job:", err.message)
      setError("Failed to add service. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteClick = async attachmentId => {
    if (window.confirm("Are you sure you want to delete this attachment?")) {
      setDeletingAttachmentId(attachmentId) // Start spinner
      try {
        await handleRemoveAttachment(attachmentId)
      } catch (error) {
        console.error("Error deleting attachment:", error)
      } finally {
        setDeletingAttachmentId(null) // Stop spinner
      }
    }
  }

  const handleGenerateSignoffPDF = async () => {
    if (!job.eta) {
      alert("Cannot generate signoff PDF: Please set the Job ETA")
      return
    }

    try {
      const pdfBytes = await generateManagerSignoffPDF({
        companyName: companyName,
        jobName: job.name,
        eta: job.eta,
        workOrderNumber: job.workOrderId,
        jobDisplayId: job.displayId,
        jobLocation: job.location,
        currentDate: dayjs().format("MMM D, YYYY"),
      })

      // Create a blob and download the PDF
      const blob = new Blob([pdfBytes], { type: "application/pdf" })
      const filename = `manager-signoff-${job.displayId}.pdf`

      // Upload to Firebase Storage
      const storageRef = ref(storage, `companies/${companyId}/jobs/${id}/signoffs/${filename}`)
      await uploadBytes(storageRef, blob)
      const downloadUrl = await getDownloadURL(storageRef)

      // Update job document with signoff URL
      const jobRef = doc(db, `companies/${companyId}/jobs`, id)
      await updateDoc(jobRef, {
        signoffUrl: downloadUrl,
        signoffFilename: filename,
        updatedAt: new Date(),
      })

      // Update local state
      setJob(prevJob => ({
        ...prevJob,
        signoffUrl: downloadUrl,
        signoffFilename: filename,
      }))

      // Create a download link
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error generating PDF:", error)
    }
  }

  const handleUserAssign = async user => {
    try {
      const jobRef = doc(db, `companies/${companyId}/jobs`, id)
      if (user === null) {
        await updateDoc(jobRef, {
          assignedTo: deleteField(),
          updatedAt: new Date(),
        })
      } else {
        await updateDoc(jobRef, {
          assignedTo: user.id,
          updatedAt: new Date(),
        })
      }
      setIsUserAssignmentModalVisible(false)
    } catch (error) {
      console.error("Error assigning user:", error)
    }
  }

  const handleTypeToggle = async () => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, id)
      if (job.type) {
        await updateDoc(docRef, { type: deleteField() })
      } else {
        await updateDoc(docRef, { type: "Emergency" })
      }
    } catch (error) {
      console.error("Error toggling job type:", error.message)
    }
  }

  const handleSubcontractorSelect = async subcontractor => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, id)
      await updateDoc(docRef, {
        subcontractor: subcontractor,
        updatedAt: new Date(),
      })

      setJob(prevJob => ({
        ...prevJob,
        subcontractor: subcontractor,
      }))
    } catch (error) {
      console.error("Error updating subcontractor:", error)
    } finally {
      setSubcontractorModalOpen(false)
    }
  }

  if (error) {
    return <p className="text-red-500 p-6">{error}</p>
  }

  if (!job) {
    return <p className="p-6 text-gray-900">Loading...</p>
  }

  const runningTotal = (
    job.services?.reduce((sum, service) => sum + (Number(service.sell) || 0) * (Number(service.quantity) || 1), 0) +
    changeOrders
      .filter(co => co.status?.toLowerCase() === "approved")
      .reduce(
        (sum, co) => sum + (co.services || []).reduce((coSum, service) => coSum + (Number(service.sell) || 0) * (Number(service.quantity) || 1), 0),
        0,
      )
  ).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })

  // FRANK: show/hide save button only if changes are made
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="w-auto mx-auto bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <BackButton to="jobs" />
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={job.type === "Emergency"}
                onChange={handleTypeToggle}
                className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
              />
              <label className={`text-sm font-medium text-white px-3 py-1 rounded-full ${job.type === "Emergency" ? "bg-red-500" : "bg-gray-300"}`}>
                Emergency Job
              </label>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {assignedUser ? (
              <div className="flex items-center gap-2">
                <span className="text-gray-700">Assigned to: {assignedUser.name}</span>
                <button onClick={() => setIsUserAssignmentModalVisible(true)} className="text-blue-500 hover:text-blue-700">
                  <FaPersonCirclePlus size={22} />
                </button>
              </div>
            ) : (
              <button onClick={() => setIsUserAssignmentModalVisible(true)} className="text-blue-500 hover:text-blue-700 flex items-center gap-2">
                <FaPersonCirclePlus size={22} />
                <span>Assign User</span>
              </button>
            )}
          </div>
        </div>
        <div className="flex justify-end mb-6 gap-4">
          <div className="flex items-center gap-2">
            <button
              onClick={handleGenerateSignoffPDF}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center gap-2"
            >
              <FaFileInvoice size={20} />
              Generate Signoff
            </button>
            {job.signoffUrl && (
              <button
                onClick={() => {
                  setPreviewAttachment({
                    url: job.signoffUrl,
                    name: job.signoffFilename,
                  })
                  setIsPdfPreview(true)
                }}
                className="text-blue-500 hover:text-blue-700 mr-6"
                title="Preview Signoff"
              >
                <FaEye size={20} />
              </button>
            )}
          </div>
          <CopyOrderButton orderData={editableJob} companyId={companyId} currentType="job" />
        </div>
        <div className="flex justify-between items-center mb-6">
          <div className="">
            <h1 className="text-2xl font-bold text-gray-900">{job.displayId || "N/A"}</h1>
            {job.originalOrderId && (
              <div className="flex gap-2 items-center">
                <h1 className="text-sm font-light text-gray-900">Copied From:</h1>
                <a
                  onClick={() => {
                    const orderType = getOrderTypeFromId(job.originalOrderDisplayId)
                    if (orderType) {
                      router.push(`/${orderType}/${job.originalOrderId}`)
                    }
                  }}
                  className="underline text-blue-600 hover:text-blue-800 cursor-pointer"
                >
                  {job.originalOrderDisplayId || "N/A"}
                </a>
              </div>
            )}
          </div>
          {/* {job.approvalStatus ? (
            <span className="px-4 py-2 rounded-full text-white font-bold bg-yellow-500">Pending Approval</span>
          ) : (
          )} */}
          <div className="flex flex-row gap-6 bg-gray-100 justify-around items-center rounded-lg p-6">
            <div className="flex col-span-1 gap-2">
              <label className="block text-gray-700 font-bold">Work Order ID:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="workOrderId"
                  placeholder="Enter work order ID"
                  value={editableJob?.workOrderId || ""}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{job?.workOrderId || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Created Date:</label>
              <p className="text-gray-900">{dayjs(job?.createdAt.toDate()).format("M/D/YYYY") || "N/A"}</p>
            </div>
            <div className="flex col-span-1 gap-2">
              <label className="block text-gray-700 font-bold">Term:</label>
              <p>{customers.find(c => c.id === job.customer.id)?.term || "N/A"}</p>
            </div>
            <div className="flex col-span-1 gap-2">
              <label className="block text-gray-700 font-bold">Running Total:</label>
              <p>${runningTotal}</p>
            </div>
            <div className="flex col-span-1 gap-2 items-center">
              <label className="block text-gray-700 font-bold">ETA:</label>
              {job.eta ? (
                <div className="flex items-center gap-2">
                  <button onClick={() => setEtaModalOpen(true)} className="text-gray-900 hover:text-blue-500 text-left">
                    <div>
                      {dayjs(job.eta?.toDate?.() || job.eta)
                        .tz(getTimezoneFromLocation(job.location))
                        .format("M/D/YY")}{" "}
                      {dayjs(job.eta?.toDate?.() || job.eta)
                        .tz(getTimezoneFromLocation(job.location))
                        .format("h:mm")}
                      {dayjs(job.eta?.toDate?.() || job.eta)
                        .tz(getTimezoneFromLocation(job.location))
                        .format("a")
                        .toLowerCase()}{" "}
                      {getTimezoneAbbr(getTimezoneFromLocation(job.location))}
                    </div>
                  </button>
                </div>
              ) : (
                <button onClick={() => setEtaModalOpen(true)} className="text-blue-500 hover:text-blue-700">
                  + Set ETA
                </button>
              )}
            </div>
          </div>
          <div className={`col-span-1 gap-2 items-center ${!isEditing && "flex"}`}>
            <label className="block text-gray-700 font-bold">Status:</label>
            <StatusPickerWithColor
              type="jobs"
              currentStatus={job?.status}
              onStatusChange={newStatus => handleStatusChange(job.id, newStatus)}
              className="max-w-xs"
            />
          </div>
          {/* {job.approvalStatus ? (
            <span className="px-4 py-2 rounded-full text-white font-bold bg-yellow-500">Pending Approval</span>
          ) : (
            <span
              className={`px-4 py-2 rounded-full text-white font-bold ${
                job.status === "New" ? "bg-green-500" : job.status === "In Progress" ? "bg-blue-500" : "bg-gray-500"
              }`}
            >
              {job.status}
            </span>
          )} */}
        </div>
        <div className="relative">
          <div className="flex justify-end mb-4">
            {!isEditing ? (
              <div className="flex gap-4">
                <button onClick={handleEditToggle} className="text-blue-500 hover:text-blue-700">
                  <HiPencilAlt size={24} />
                </button>
              </div>
            ) : (
              <div className="flex">
                <button onClick={handleSave} className="text-green-500 hover:text-green-700">
                  <HiCheck size={24} />
                </button>
                <button onClick={handleEditToggle} className="text-red-500 hover:text-red-700 ml-4">
                  <HiX size={24} />
                </button>
              </div>
            )}
          </div>
        </div>
        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Job Name:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="name"
                  placeholder="Enter job name"
                  value={editableJob?.name || ""}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{job?.name || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <DatePicker
                label="Due Date"
                value={
                  editableJob?.dueDate
                    ? typeof editableJob.dueDate === "object"
                      ? dayjs(editableJob.dueDate.toDate()).format("YYYY-MM-DD")
                      : dayjs(editableJob.dueDate).format("YYYY-MM-DD")
                    : ""
                }
                onChange={d => handleChange({ target: { name: "dueDate", value: d } })}
                isEditing={isEditing}
              />
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <DatePicker
                label="Estimated Completion Date"
                value={editableJob.estimatedCompletionDate || new Date().toISOString().split("T")[0]}
                onChange={d => handleChange({ target: { name: "estimatedCompletionDate", value: d } })}
                isEditing={isEditing}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Customer:</label>
              {isEditing ? (
                <>
                  <input
                    type="text"
                    name="customer.name"
                    value={editableJob.customer.name}
                    onChange={handleChange}
                    placeholder="Enter customer name"
                    className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                    required
                  />
                  {filteredCustomers.length > 0 && editableJob.customer.name.length > 0 && (
                    <ul className="absolute z-10 bg-white border border-gray-300 rounded shadow-lg mt-1 w-full max-h-40 overflow-y-auto">
                      {filteredCustomers.map(customer => (
                        <li
                          key={customer.id}
                          onClick={() => handleCustomerSelect(customer)}
                          className="px-4 py-2 cursor-pointer hover:bg-gray-100 text-gray-500"
                        >
                          {customer.name}
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : (
                <p className="text-gray-900">{job?.customer.name || "N/A"}</p>
              )}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className={`col-span-2 gap-2 ${!isEditing && "flex"}`}>
                <label className="block text-gray-700 font-bold">Email:</label>
                {isEditing ? (
                  <input
                    type="text"
                    name="customer.email"
                    placeholder="Enter customer email"
                    value={editableJob?.customer.email || ""}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                ) : (
                  <div className="flex items-center gap-2">
                    <p className="text-gray-900">{job?.customer.email || "N/A"}</p>
                    {job?.customer.email && (
                      <button
                        onClick={() => {
                          setEmailModalRecipient(job.customer.email)
                          setIsEmailModalOpen(true)
                        }}
                        className="text-blue-500 hover:text-blue-700"
                      >
                        <FaEnvelope size={16} />
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Location:</label>
              {isEditing ? (
                <AddressAutocomplete
                  name="location"
                  placeholder="Enter job location"
                  value={editableJob?.location || ""}
                  onAddressSelect={location => handleChange({ target: { name: "location", value: location } })}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{job?.location || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Contact Name:</label>
              {isEditing ? (
                <>
                  <input
                    type="text"
                    name="siteContactName"
                    value={editableJob.siteContactName}
                    onChange={handleChange}
                    placeholder="Enter site contact name"
                    className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                    required
                  />
                  {filteredContacts.length > 0 && editableJob.siteContactName.length > 0 && (
                    <ul className="absolute z-10 bg-white border border-gray-300 rounded shadow-lg mt-1 w-full max-h-40 overflow-y-auto">
                      {filteredContacts.map(contact => (
                        <li
                          key={contact.id}
                          onClick={() => handleContactSelect(contact)}
                          className="px-4 py-2 cursor-pointer hover:bg-gray-100 text-gray-500"
                        >
                          {contact.name}
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : (
                <p className="text-gray-900">{job?.siteContactName || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Contact Email:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="siteContactEmail"
                  placeholder="Enter contact email"
                  value={editableJob?.siteContactEmail || ""}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <div className="flex items-center gap-2">
                  <p className="text-gray-900">{job?.siteContactEmail || "N/A"}</p>
                  {job?.siteContactEmail && (
                    <button
                      onClick={() => {
                        setEmailModalRecipient(job.siteContactEmail)
                        setIsEmailModalOpen(true)
                      }}
                      className="text-blue-500 hover:text-blue-700"
                    >
                      <FaEnvelope size={16} />
                    </button>
                  )}
                </div>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Contact Phone:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="siteContactPhone"
                  placeholder="Enter contact phone"
                  value={editableJob?.siteContactPhone || ""}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{formatPhone(job?.siteContactPhone) || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">NTE:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="nte"
                  placeholder="Enter NTE"
                  value={editableJob?.nte || ""}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">
                  $
                  {isNaN(Number(job?.nte)) ? "N/A" : Number(job?.nte).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Days Left:</label>
              <p className={`px-2 rounded ${getDueDateClass(editableJob?.dueDate)}`}>
                {editableJob?.dueDate ? `${dayjs(editableJob.dueDate?.toDate?.() || editableJob.dueDate).diff(dayjs(), "day")} days` : "N/A"}
              </p>
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <DatePicker
                label="Date Completed"
                value={
                  isEditing
                    ? editableJob?.completedAt
                      ? typeof editableJob.completedAt === "object"
                        ? dayjs(editableJob.completedAt.toDate()).format("YYYY-MM-DD")
                        : dayjs(editableJob.completedAt).format("YYYY-MM-DD")
                      : ""
                    : job?.completedAt
                    ? typeof job.completedAt === "object"
                      ? dayjs(job.completedAt.toDate()).format("YYYY-MM-DD")
                      : dayjs(job.completedAt).format("YYYY-MM-DD")
                    : ""
                }
                onChange={d => handleChange({ target: { name: "completedAt", value: d } })}
                isEditing={isEditing}
              />
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <DatePicker
                label="Date Invoiced"
                value={
                  isEditing
                    ? editableJob?.invoicedAt
                      ? typeof editableJob.invoicedAt === "object"
                        ? dayjs(editableJob.invoicedAt.toDate()).format("YYYY-MM-DD")
                        : dayjs(editableJob.invoicedAt).format("YYYY-MM-DD")
                      : ""
                    : job?.invoicedAt
                    ? typeof job.invoicedAt === "object"
                      ? dayjs(job.invoicedAt.toDate()).format("YYYY-MM-DD")
                      : dayjs(job.invoicedAt).format("YYYY-MM-DD")
                    : ""
                }
                onChange={d => handleChange({ target: { name: "invoicedAt", value: d } })}
                isEditing={isEditing}
              />
            </div>
            <div className="w-full col-span-3">
              <NotesSection orderId={job.id} orderType="job" />
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Subcontractor</label>
              <div className="flex items-center gap-2">
                {job.subcontractor ? (
                  <div className="flex items-center gap-4">
                    <Tooltip.Root>
                      <Tooltip.Trigger asChild>
                        <button onClick={() => setSubcontractorModalOpen(true)} className="text-gray-900 hover:text-blue-500">
                          {job.subcontractor.name}
                        </button>
                      </Tooltip.Trigger>
                      <Tooltip.Content className="bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg">
                        <div className="flex flex-col gap-1">
                          <div>{job.subcontractor.name}</div>
                          {job.subcontractor.phone && <div>{job.subcontractor.phone}</div>}
                          {job.subcontractor.email && <div>{job.subcontractor.email}</div>}
                        </div>
                      </Tooltip.Content>
                    </Tooltip.Root>
                    {job.subcontractor.email && (
                      <button
                        onClick={() => {
                          setEmailModalRecipient(job.subcontractor.email)
                          setIsEmailModalOpen(true)
                        }}
                        className="text-blue-500 hover:text-blue-700"
                        title="Email Subcontractor"
                      >
                        <FaEnvelope size={16} />
                      </button>
                    )}
                    <button
                      onClick={async () => {
                        try {
                          const docRef = doc(db, `companies/${companyId}/jobs`, id)
                          await updateDoc(docRef, {
                            subcontractor: deleteField(),
                            updatedAt: new Date(),
                          })
                          setJob(prevJob => ({
                            ...prevJob,
                            subcontractor: null,
                          }))
                        } catch (error) {
                          console.error("Error removing subcontractor:", error)
                        }
                      }}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FaTimes size={14} />
                    </button>
                  </div>
                ) : (
                  <button onClick={() => setSubcontractorModalOpen(true)} className="text-blue-500 hover:text-blue-700">
                    + Add Subcontractor
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
        <Divider />
        {/* Services Section */}
        <JobServicesTable
          job={job}
          subcontractors={subcontractors}
          globalSubcontractor={globalSubcontractor}
          onCreateInvoice={() => console.log("CREATE INVOICE")}
        />
        {/* Add Service Section */}
        <ServicePicker order={job} onAddService={handleAddExistingService} />
        {/* Add Rate Item Section */}
        {/* <ServiceSelector order={job} onAddService={handleAddExistingService} /> */}
        <Divider />
        {/* FRANK: Add services table per number of items in job.changeOrders */}
        <ChangeOrdersManager job={job} companyId={companyId} />

        <Divider />
        <CommentsSection orderId={job.id} orderType="job" />
        <Divider />
        {/* Quotes Section */}
        <div className="mt-6">
          <div className="flex items-center mb-2">
            <div className="cursor-pointer" onClick={() => setIsQuotesSectionExpanded(prev => !prev)}>
              {isQuotesSectionExpanded ? (
                <HiChevronUp className="text-gray-900 w-6 h-6 mr-2" />
              ) : (
                <HiChevronDown className="text-gray-900 w-6 h-6 mr-2" />
              )}
            </div>
            <h2 className="text-xl font-bold text-gray-900">Quotes {quotes?.length ? `(${quotes.length})` : ""}</h2>
          </div>
          {isQuotesSectionExpanded && (
            <div className="overflow-x-auto">
              {quotes && quotes.length > 0 ? (
                <table className="table-auto w-full border-collapse border border-gray-300 text-left">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border p-4 text-gray-900 w-1/6">Display ID</th>
                      <th className="border p-4 text-gray-900 w-1/6">Timestamp</th>
                      <th className="border p-4 text-gray-900 w-fit">Services</th>
                      {/* <th className="border p-4 text-gray-900 w-24">Sell</th> */}
                      <th className="border p-4 text-gray-900 w-1">Subtotal</th>
                      <th className="border p-4 text-gray-900 w-1">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedById(quotes)?.map(quote => (
                      <tr key={quote.id} className="hover:bg-gray-50">
                        <td className="border p-2 text-blue-500 underline cursor-pointer">
                          <a key={quote.id} href={quote.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                            {quote.displayId}
                          </a>
                        </td>
                        <td className="border p-2 text-gray-800">{dayjs(quote.createdAt.toDate()).format("MMM D, YYYY h:mma")}</td>
                        <td className="border p-2 text-gray-800">{quote.services.map(q => q.name).join(", ")}</td>
                        {/* <td className="border p-2 text-gray-800"> */}
                        {/* ${quote.subtotal?.toFixed(2)} */}
                        {/* {quote.items?.map(item => (
                          <div key={item.id}>
                            {item.name}: ${item.sell.toFixed(2)}
                          </div>
                        ))} */}
                        {/* </td> */}
                        <td className="border p-2 text-gray-800">
                          ${quote.subtotal?.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </td>
                        <td className="p-4 border border-gray-300">
                          <div className="flex justify-evenly gap-2">
                            <button onClick={e => deleteQuote(quote.id)} className="text-red-500 hover:text-red-700">
                              <FaTrashAlt size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-gray-500">No quotes available.</p>
              )}
            </div>
          )}
        </div>
        <Divider />
        {/* CSAs Section */}
        <div className="mt-6">
          <div className="flex items-center mb-2">
            <div className="cursor-pointer" onClick={() => setIsCSASectionExpanded(prev => !prev)}>
              {isCSASectionExpanded ? (
                <HiChevronUp className="text-gray-900 w-6 h-6 mr-2" />
              ) : (
                <HiChevronDown className="text-gray-900 w-6 h-6 mr-2" />
              )}
            </div>
            <h2 className="text-xl font-bold text-gray-900">CSAs {csas?.length ? `(${csas.length})` : ""}</h2>
          </div>

          {isCSASectionExpanded && (
            <div className="overflow-x-auto">
              {csas && csas.length > 0 ? (
                <table className="table-auto w-full border-collapse border border-gray-300 text-left">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border p-4 text-gray-900 w-1/6">Display ID</th>
                      <th className="border p-4 text-gray-900 w-1/6">Timestamp</th>
                      <th className="border p-4 text-gray-900 w-fit">Services</th>
                      <th className="border p-4 text-gray-900 w-1/5">Subcontractor</th>
                      <th className="border p-4 text-gray-900 w-1">Subtotal</th>
                      <th className="border p-4 text-gray-900 w-1">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedById(csas)?.map(csa => (
                      <tr key={csa.id} className="hover:bg-gray-50">
                        <td className="border p-2 text-blue-500 underline cursor-pointer">
                          <a key={csa.id} href={csa.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                            {csa.displayId}
                          </a>
                        </td>
                        <td className="border p-2 text-gray-800">{dayjs(csa.createdAt.toDate()).format("MMM D, YYYY h:mma")}</td>
                        <td className="border p-2 text-gray-800 break-words">
                          {csa.services?.length > 0 ? csa.services.map(service => service.name).join(", ") : "No services listed"}
                        </td>
                        <td className="border p-2 text-gray-800">{csa.subcontractor?.name || "Unassigned"}</td>
                        <td className="border p-2 text-gray-800">
                          ${csa.subtotal?.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || "0.00"}
                        </td>
                        <td className="p-4 border border-gray-300">
                          <div className="flex justify-evenly gap-2">
                            <button onClick={e => deleteCSA(csa.id)} className="text-red-500 hover:text-red-700">
                              <FaTrashAlt size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-gray-500">No CSAs available.</p>
              )}
            </div>
          )}
        </div>
        <Divider />
        {/* Invoices Section */}
        {/* FRANK: individual cost, sell, and subtotal works but not for master invoice */}
        <div className="mt-6">
          <div className="flex items-center mb-2">
            <div className="cursor-pointer" onClick={() => setIsInvoiceSectionExpanded(prev => !prev)}>
              {isInvoiceSectionExpanded ? (
                <HiChevronUp className="text-gray-900 w-6 h-6 mr-2" />
              ) : (
                <HiChevronDown className="text-gray-900 w-6 h-6 mr-2" />
              )}
            </div>
            <h2 className="text-xl font-bold text-gray-900">Invoices {invoices?.length ? `(${invoices.length})` : ""}</h2>
          </div>
          {isInvoiceSectionExpanded && (
            <div className="overflow-x-auto">
              {invoices && invoices.length > 0 ? (
                <table className="table-auto w-full border-collapse border border-gray-300 text-left">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border p-4 text-gray-900 w-1/5">Display ID</th>
                      <th className="border p-4 text-gray-900 w-1/5">Timestamp</th>
                      <th className="border p-4 text-gray-900 w-1/5">Customer</th>
                      <th className="border p-4 text-gray-900 w-1">Subtotal</th>
                      <th className="border p-4 text-gray-900 w-1">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedById(invoices)?.map(invoice => (
                      <tr key={invoice.id} className="hover:bg-gray-50">
                        <td className="border p-2 text-blue-500 underline cursor-pointer">
                          <a key={invoice.id} href={invoice.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                            {invoice.displayId}
                          </a>
                        </td>
                        <td className="border p-2 text-gray-800">{dayjs(invoice.createdAt.toDate()).format("MMM D, YYYY h:mma")}</td>
                        <td className="border p-2 text-gray-800">{invoice.jobDetails.customer.name}</td>
                        <td className="border p-2 text-gray-800">${invoice.subtotal?.toFixed(2)}</td>
                        <td className="p-4 border border-gray-300">
                          <div className="flex justify-evenly gap-2">
                            <button onClick={e => deleteInvoice(invoice.id)} className="text-red-500 hover:text-red-700">
                              <FaTrashAlt size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-gray-500">No invoices available.</p>
              )}
            </div>
          )}
        </div>
        <Divider />
        {/* Attachments Section */}
        <div className="col-span-3">
          <h2 className="text-lg font-bold mb-4 text-gray-900">Attachments</h2>
          {/* Hidden file input */}
          <input ref={fileInputRef} type="file" multiple style={{ display: "none" }} onChange={handleFileSelect} />
          {/* Drop Zone */}
          <div
            className={`relative border-2 flex justify-center items-center text-center rounded mb-6 cursor-pointer text-gray-500 h-48 ${
              dragging ? "border-blue-500 bg-blue-100" : "border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current.click()}
          >
            <span>{dragging ? "Release to upload" : "Drag & Drop files here or click to upload"}</span>
            {uploading && (
              <div className="absolute inset-0 flex justify-center items-center bg-white bg-opacity-50 z-10">
                <div className="loader border-t-4 border-blue-500 w-8 h-8 rounded-full animate-spin"></div>
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {(attachments || []).map(attachment => {
              const mimetype = attachment.mimetype || "" // make sure this field exists or infer it from attachment.name
              const isPDF = mimetype === "application/pdf" || attachment.name.toLowerCase().endsWith(".pdf")
              return (
                <div key={attachment.id} className="border p-2 rounded shadow-md flex items-center gap-4">
                  {isPDF ? (
                    <iframe src={attachment.url} title={attachment.name} className="w-16 h-16" style={{ border: "none" }} />
                  ) : (
                    <img src={attachment.url} alt={attachment.name} className="w-16 h-16 object-cover rounded" />
                  )}
                  <div className="flex-1">
                    <a href={attachment.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                      {attachment.name}
                    </a>
                  </div>
                  <div className="flex gap-4 pr-1">
                    {/* <button onClick={e => handleOpenModal(e, attachment)} className="text-blue-500 hover:text-blue-700 p-1" title="View Attachment">
                      <FaEye size={16} />
                    </button> */}
                    <button
                      type="button"
                      onClick={() => handleDeleteClick(attachment.id)}
                      className={`text-red-500 hover:text-red-700 p-1 ${deletingAttachmentId === attachment.id ? "cursor-wait" : ""}`}
                      title="Delete Attachment"
                      disabled={deletingAttachmentId === attachment.id}
                    >
                      {deletingAttachmentId === attachment.id ? (
                        <div className="absolute inset-0 flex justify-center items-center">
                          <div className="loader border-t-4 border-red-500 w-4 h-4 rounded-full animate-spin"></div>
                        </div>
                      ) : (
                        <FaTrashAlt size={16} />
                      )}
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
        <Divider />
        {/* <div className="mt-6">
          <SlackChat channelId={job.slackChannelId} jobId={job.id} />
        </div>
        <Divider /> */}
        <div className="mt-6 flex gap-4 justify-end">
          <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded" onClick={() => router.push(`/jobs/edit/${id}`)}>
            Edit Job
          </button>
          {admin && (
            <button className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded" onClick={handleDelete}>
              Delete Job
            </button>
          )}
        </div>
        <div className="flex justify-end p-4">
          <button
            onClick={() => setIsChangeOrderOpen(true)}
            className="p-2 rounded-full bg-blue-500 text-white hover:bg-blue-600 flex items-center justify-center"
            title="New Change Order"
          >
            <FaPlus size={20} />
          </button>
        </div>
        {/* <ChangeOrderModal isOpen={isChangeOrderOpen} job={job} onClose={() => setIsChangeOrderOpen(false)} onSave={handleSaveChangeOrder} /> */}
      </div>
      {isUserAssignmentModalVisible && (
        <UserAssignmentModal
          onClose={() => setIsUserAssignmentModalVisible(false)}
          onAssign={handleUserAssign}
          defaultUserId={job.assignedTo}
          type="job"
          entityId={job.id}
          entityName={job.name || job.id}
          entityDisplayId={job.displayId}
          assignedTo={job?.assignedTo}
        />
      )}
      <SubcontractorSelectionModal
        isOpen={subcontractorModalOpen}
        onClose={() => setSubcontractorModalOpen(false)}
        onSelect={handleSubcontractorSelect}
        currentSubcontractor={job?.subcontractor}
      />
      <ETAPickerModal
        isOpen={etaModalOpen}
        onClose={() => setEtaModalOpen(false)}
        jobId={id}
        currentETA={job?.eta}
        onETAChange={newETA => {
          setJob(prevJob => ({
            ...prevJob,
            eta: newETA,
          }))
        }}
      />
      <EmailCompositionModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        recipientEmail={emailModalRecipient}
        jobId={job?.id}
        defaultSubject={`Regarding Job ${job?.displayId}`}
        job={job}
      />
      {/* PDF Preview Modal */}
      {previewAttachment && isPdfPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-[200]">
          <div className="bg-white w-11/12 max-w-5xl h-5/6 rounded shadow-lg overflow-hidden p-6 flex flex-col">
            <div className="flex justify-between items-center mb-4 border-b pb-2">
              <h2 className="text-lg font-bold text-gray-900">PDF Preview</h2>
              <button
                onClick={() => {
                  setPreviewAttachment(null)
                  setIsPdfPreview(false)
                }}
                className="text-red-500 hover:text-red-700 font-bold"
              >
                Close
              </button>
            </div>
            <div className="flex-1 w-full h-full">
              <iframe
                src={`${previewAttachment.url}#toolbar=0`}
                title={previewAttachment.name}
                className="w-full h-full border-0"
                style={{ backgroundColor: "white" }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
