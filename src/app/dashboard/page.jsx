"use client"

import { useEffect, useState } from "react"
import { db } from "../../firebase"
import { collection, getDocs, doc, getDoc, query, where, getDocsFromCache, updateDoc } from "firebase/firestore"
import ProtectedRoute from "@/components/ProtectedRoute"
import { Bar } from "react-chartjs-2"
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js"
import { useSelector, useDispatch } from "react-redux"
import { useUser } from "@/hooks/useUser"
import StatusPickerWithColor from "@/components/StatusPickerWithColor"
import DatePicker from "@/components/DatePicker"
import SlackChat from "@/components/SlackChat"
import { IoChatbubbleEllipses } from "react-icons/io5"
import { FaChevronRight, FaFileExport, FaCog } from "react-icons/fa"
import dayjs from "dayjs"
import isSameOrBefore from "dayjs/plugin/isSameOrBefore"
import isSameOrAfter from "dayjs/plugin/isSameOrAfter"
import quarterOfYear from "dayjs/plugin/quarterOfYear"
import { setStatusColors } from "@/slices/statusColorsSlice"

// Widget system imports
import { useWidgets } from "@/hooks/useWidgets"
import WidgetContainer from "@/components/widgets/WidgetContainer"
import WidgetMenu from "@/components/widgets/WidgetMenu"
import { renderWidget } from "@/components/widgets/WidgetRegistry"

// Register dayjs plugins
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(quarterOfYear)

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("jobs")
  const [isSlackDrawerOpen, setIsSlackDrawerOpen] = useState(false)
  const [newMessageCount, setNewMessageCount] = useState(0)
  const [isWidgetMenuOpen, setIsWidgetMenuOpen] = useState(false)
  const [jobs, setJobs] = useState([])
  const [opportunities, setOpportunities] = useState([])
  const [leads, setLeads] = useState([])
  const [filteredJobs, setFilteredJobs] = useState([])
  const [filteredOpportunities, setFilteredOpportunities] = useState([])
  const [filteredLeads, setFilteredLeads] = useState([])
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("All")
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")
  const [jobChartData, setJobChartData] = useState({})
  const [opportunityChartData, setOpportunityChartData] = useState({})
  const [leadChartData, setLeadChartData] = useState({})
  const [invoicedTotal, setInvoicedTotal] = useState(0)
  const [adminCosts, setAdminCosts] = useState(0)
  const [netIncome, setNetIncome] = useState(0)
  const [adminCostPercentage, setAdminCostPercentage] = useState(30)
  const [jobStatuses, setJobStatuses] = useState([])
  const [opportunityStatuses, setOpportunityStatuses] = useState([])
  const [leadStatuses, setLeadStatuses] = useState([])
  const [invoicedTimeRange, setInvoicedTimeRange] = useState("ytd")
  const [invoicedStartDate, setInvoicedStartDate] = useState("")
  const [invoicedEndDate, setInvoicedEndDate] = useState("")
  const [filteredInvoicedJobs, setFilteredInvoicedJobs] = useState([])
  const [commissionTimeRange, setCommissionTimeRange] = useState("7days")
  const [commissionStartDate, setCommissionStartDate] = useState("")
  const [commissionEndDate, setCommissionEndDate] = useState("")
  const [jobChangeOrders, setJobChangeOrders] = useState({})
  const { companyId } = useSelector(state => state.company)
  const { admin } = useUser()
  const { user } = useSelector(state => state.auth)
  const dispatch = useDispatch()
  const statusColors = useSelector(state => state.statusColors)

  // Widget system
  const { enabledWidgets, availableWidgetTypes, loading: widgetsLoading, addWidget, toggleWidget, removeWidget } = useWidgets()

  // Add new state for caching
  const [isLoading, setIsLoading] = useState(true)
  const [lastFetchTime, setLastFetchTime] = useState(null)
  const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds

  // Add grossProfit state
  const [grossProfit, setGrossProfit] = useState(0)

  // Optimized fetch function for all data
  const fetchAllData = async () => {
    if (!companyId || !user?.id) return

    try {
      setIsLoading(true)
      const now = Date.now()

      // Check if we can use cached data
      if (lastFetchTime && now - lastFetchTime < CACHE_DURATION) {
        console.log("Using cached data")
        setIsLoading(false)
        return
      }

      // Batch fetch all collections
      const [jobsSnapshot, opportunitiesSnapshot, leadsSnapshot, statusesDoc, colorsDoc] = await Promise.all([
        getDocs(collection(db, `companies/${companyId}/jobs`)),
        getDocs(collection(db, `companies/${companyId}/opportunities`)),
        getDocs(collection(db, `companies/${companyId}/leads`)),
        getDoc(doc(db, `companies/${companyId}/settings`, "statuses")),
        getDoc(doc(db, `companies/${companyId}/settings`, "statusColors")),
      ])

      // Process jobs data
      const jobList = jobsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }))

      // Filter jobs based on admin status
      const filteredJobList = admin ? jobList : jobList.filter(job => job.assignedTo === user.id)

      // Batch fetch change orders for all jobs
      const changeOrdersPromises = filteredJobList.map(job => getDocs(collection(db, `companies/${companyId}/jobs/${job.id}/changeOrders`)))
      const changeOrdersSnapshots = await Promise.all(changeOrdersPromises)

      // Process change orders
      const changeOrdersMap = {}
      changeOrdersSnapshots.forEach((snapshot, index) => {
        const jobId = filteredJobList[index].id
        changeOrdersMap[jobId] = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))
      })

      // Process opportunities
      const opportunityList = opportunitiesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }))
      const filteredOpportunityList = opportunityList.filter(opportunity => opportunity.assignedTo === user.id)

      // Process leads
      const leadList = leadsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }))
      const filteredLeadList = leadList.filter(lead => lead.assignedTo === user.id)

      // Process statuses and colors
      if (statusesDoc.exists()) {
        const data = statusesDoc.data()
        setJobStatuses(data.jobs || [])
        setOpportunityStatuses(data.opportunities || [])
        setLeadStatuses(data.leads || [])
      }
      if (colorsDoc.exists()) {
        dispatch(setStatusColors(colorsDoc.data()))
      }

      // Update all state at once
      setJobs(filteredJobList)
      setFilteredJobs(filteredJobList)
      setOpportunities(filteredOpportunityList)
      setFilteredOpportunities(filteredOpportunityList)
      setLeads(filteredLeadList)
      setFilteredLeads(filteredLeadList)
      setJobChangeOrders(changeOrdersMap)

      // Calculate initial totals
      const { total, profit } = calculateTotalsForPeriod(filteredJobList)
      setInvoicedTotal(total)
      setGrossProfit(profit)
      const adminCosts = profit * (adminCostPercentage / 100)
      setAdminCosts(adminCosts)
      setNetIncome(profit - adminCosts)

      // Update last fetch time
      setLastFetchTime(now)
    } catch (error) {
      console.error("Error fetching data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Replace multiple useEffects with a single one for initial data fetch
  useEffect(() => {
    fetchAllData()
  }, [companyId, user?.id, admin])

  // Add refresh function
  const handleRefresh = () => {
    setLastFetchTime(null) // Force refresh by clearing cache
    fetchAllData()
  }

  // Update the chart data when jobs or filters change
  useEffect(() => {
    const actualStatuses = [...new Set(jobs.map(job => job.status))]

    // Jobs Chart Data
    const jobStatusCounts = jobs.reduce((acc, job) => {
      if (!job.status) {
        return acc
      }
      acc[job.status] = (acc[job.status] || 0) + 1
      return acc
    }, {})

    // Now ensure all statuses from jobStatuses are in the counts
    const finalJobStatusCounts = jobStatuses.reduce((acc, status) => {
      acc[status] = jobStatusCounts[status] || 0
      return acc
    }, {})

    // Opportunities Chart Data
    const opportunityStatusCounts = opportunities.reduce(
      (acc, opportunity) => {
        acc[opportunity.status] = (acc[opportunity.status] || 0) + 1
        return acc
      },
      opportunityStatuses.reduce((acc, status) => {
        acc[status] = 0
        return acc
      }, {}),
    )

    // Leads Chart Data
    const leadStatusCounts = leads.reduce(
      (acc, lead) => {
        acc[lead.status] = (acc[lead.status] || 0) + 1
        return acc
      },
      leadStatuses.reduce((acc, status) => {
        acc[status] = 0
        return acc
      }, {}),
    )

    setJobChartData({
      labels: Object.keys(finalJobStatusCounts),
      datasets: [
        {
          label: "Job Status Overview",
          data: Object.values(finalJobStatusCounts),
          backgroundColor: Object.keys(finalJobStatusCounts).map(status => statusColors.jobs?.[status] || "#6b7280"),
        },
      ],
    })

    setOpportunityChartData({
      labels: Object.keys(opportunityStatusCounts),
      datasets: [
        {
          label: "Opportunity Status Overview",
          data: Object.values(opportunityStatusCounts),
          backgroundColor: Object.keys(opportunityStatusCounts).map(status => statusColors.opportunities?.[status] || "#6b7280"),
        },
      ],
    })

    setLeadChartData({
      labels: Object.keys(leadStatusCounts),
      datasets: [
        {
          label: "Lead Status Overview",
          data: Object.values(leadStatusCounts),
          backgroundColor: Object.keys(leadStatusCounts).map(status => statusColors.leads?.[status] || "#6b7280"),
        },
      ],
    })
  }, [jobs, opportunities, leads, jobStatuses, opportunityStatuses, leadStatuses, statusColors])

  // Filter Data
  useEffect(() => {
    let updatedJobs = jobs
    let updatedOpportunities = opportunities
    let updatedLeads = leads

    // Apply Search Query
    if (searchQuery) {
      updatedJobs = updatedJobs.filter(job => job.title?.toLowerCase().includes(searchQuery.toLowerCase()))
      updatedOpportunities = updatedOpportunities.filter(opportunity => opportunity.title?.toLowerCase().includes(searchQuery.toLowerCase()))
      updatedLeads = updatedLeads.filter(lead => lead.title?.toLowerCase().includes(searchQuery.toLowerCase()))
    }

    // Apply Status Filter
    if (statusFilter !== "All") {
      updatedJobs = updatedJobs.filter(job => job.status === statusFilter)
      updatedOpportunities = updatedOpportunities.filter(opportunity => opportunity.status === statusFilter)
      updatedLeads = updatedLeads.filter(lead => lead.status === statusFilter)
    }

    // Apply Date Filters
    if (startDate) {
      updatedJobs = updatedJobs.filter(job => new Date(getDateFromTimestamp(job.createdAt)) >= new Date(startDate))
      updatedOpportunities = updatedOpportunities.filter(opportunity => new Date(getDateFromTimestamp(opportunity.createdAt)) >= new Date(startDate))
      updatedLeads = updatedLeads.filter(lead => new Date(getDateFromTimestamp(lead.createdAt)) >= new Date(startDate))
    }
    if (endDate) {
      updatedJobs = updatedJobs.filter(job => new Date(getDateFromTimestamp(job.createdAt)) <= new Date(endDate))
      updatedOpportunities = updatedOpportunities.filter(opportunity => new Date(getDateFromTimestamp(opportunity.createdAt)) <= new Date(endDate))
      updatedLeads = updatedLeads.filter(lead => new Date(getDateFromTimestamp(lead.createdAt)) <= new Date(endDate))
    }

    setFilteredJobs(updatedJobs)
    setFilteredOpportunities(updatedOpportunities)
    setFilteredLeads(updatedLeads)
  }, [searchQuery, statusFilter, startDate, endDate, jobs, opportunities, leads])

  // Helper function to safely convert dates
  const getDateFromTimestamp = timestamp => {
    if (!timestamp) return null
    if (typeof timestamp.toDate === "function") {
      return timestamp.toDate()
    }
    if (timestamp instanceof Date) {
      return timestamp
    }
    return new Date(timestamp)
  }

  // Filter invoiced jobs based on time range
  useEffect(() => {
    if (!jobs.length) {
      return
    }

    const now = new Date()
    let startDate = new Date()
    let endDate = new Date()

    switch (invoicedTimeRange) {
      case "7days":
        startDate.setDate(now.getDate() - 7)
        break
      case "30days":
        startDate.setDate(now.getDate() - 30)
        break
      case "quarter":
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
        break
      case "custom":
        if (invoicedStartDate && invoicedEndDate) {
          startDate = new Date(invoicedStartDate)
          endDate = new Date(invoicedEndDate)
        }
        break
    }

    const filteredJobs = jobs
      .filter(job => {
        if (!job.invoicedAt) {
          return false
        }
        const invoicedDate = getDateFromTimestamp(job.invoicedAt)
        const isInRange = invoicedDate >= startDate && invoicedDate <= endDate
        return isInRange
      })
      .sort((a, b) => getDateFromTimestamp(b.invoicedAt) - getDateFromTimestamp(a.invoicedAt))

    // Update filtered invoiced jobs for the table
    setFilteredInvoicedJobs(filteredJobs)

    const total = filteredJobs.reduce((sum, job) => {
      if (job.status === "Invoiced" || (job.status.includes("Paid") && job.services)) {
        const jobTotal = job.services.reduce((serviceSum, service) => {
          return serviceSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1)
        }, 0)
        console.log("Job total:", { id: job.id, total: jobTotal })
        return sum + jobTotal
      }
      return sum
    }, 0)

    setInvoicedTotal(total)
    setAdminCosts(total * (adminCostPercentage / 100))
    setNetIncome(total - total * (adminCostPercentage / 100))
  }, [jobs, invoicedTimeRange, invoicedStartDate, invoicedEndDate, adminCostPercentage])

  // Filter jobs based on commission time range
  useEffect(() => {
    if (!jobs.length) {
      return
    }

    const now = new Date()
    let startDate = new Date()
    let endDate = new Date()

    switch (commissionTimeRange) {
      case "7days":
        startDate.setDate(now.getDate() - 7)
        break
      case "30days":
        startDate.setDate(now.getDate() - 30)
        break
      case "quarter":
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
        break
      case "custom":
        if (commissionStartDate && commissionEndDate) {
          startDate = new Date(commissionStartDate)
          endDate = new Date(commissionEndDate)
        }
        break
    }

    const filtered = jobs.filter(job => {
      if (job.status !== "Invoiced" || !job.invoicedAt) return false
      const invoicedDate = getDateFromTimestamp(job.invoicedAt)
      return invoicedDate >= startDate && invoicedDate <= endDate
    })

    setFilteredInvoicedJobs(filtered)
  }, [jobs, commissionTimeRange, commissionStartDate, commissionEndDate])

  // Update the status of a job
  const handleStatusChange = async (jobId, newStatus) => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await updateDoc(docRef, { status: newStatus })

      // Update local state
      setJobs(prevJobs => prevJobs.map(job => (job.id === jobId ? { ...job, status: newStatus } : job)))
    } catch (error) {
      console.error("Error updating status:", error.message)
    }
  }

  // Delete a job
  const handleDelete = async jobId => {
    const confirmDelete = window.confirm("Are you sure you want to delete this job?")
    if (!confirmDelete) return

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await deleteDoc(docRef)

      // Update the local state
      setJobs(prevJobs => prevJobs.filter(job => job.id !== jobId))
      setFilteredJobs(prevFilteredJobs => prevFilteredJobs.filter(job => job.id !== jobId))
    } catch (error) {
      console.error("Error deleting job:", error.message)
    }
  }

  // Update the due date of a job
  const handleDueDateChange = async (jobId, newDueDate) => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      // Convert the date string to a Firestore timestamp
      const timestamp = newDueDate ? new Date(newDueDate) : null
      await updateDoc(docRef, { dueDate: timestamp })

      // Update local state
      setJobs(prevJobs => prevJobs.map(job => (job.id === jobId ? { ...job, dueDate: timestamp } : job)))
    } catch (error) {
      console.error("Error updating due date:", error.message)
    }
  }

  // Reset custom date range when time range changes
  useEffect(() => {
    if (invoicedTimeRange !== "custom") {
      setInvoicedStartDate("")
      setInvoicedEndDate("")
    }
    // Clear filtered jobs when time range changes
    setFilteredInvoicedJobs([])
  }, [invoicedTimeRange])

  const calculateTotalsForPeriod = jobs => {
    const now = dayjs()
    let startDate
    let endDate = now.endOf("day")

    switch (invoicedTimeRange) {
      case "ytd":
        startDate = now.startOf("year")
        break
      case "7days":
        startDate = now.subtract(7, "day").startOf("day")
        break
      case "30days":
        startDate = now.subtract(30, "day").startOf("day")
        break
      case "quarter":
        const currentQuarter = now.quarter()
        startDate = dayjs().quarter(currentQuarter).startOf("quarter")
        endDate = dayjs().quarter(currentQuarter).endOf("quarter")
        break
      case "custom":
        if (invoicedStartDate && invoicedEndDate) {
          startDate = dayjs(invoicedStartDate).startOf("day")
          endDate = dayjs(invoicedEndDate).endOf("day")
        }
        break
    }

    const filteredJobs = jobs
      .filter(job => {
        if (job.status === "Invoiced" || job.status.includes("Paid")) {
          const invoicedDate = dayjs(getDateFromTimestamp(job.invoicedAt))
          return invoicedDate.isSameOrAfter(startDate) && invoicedDate.isSameOrBefore(endDate)
        }
        return false
      })
      .sort((a, b) => dayjs(getDateFromTimestamp(b.invoicedAt)).valueOf() - dayjs(getDateFromTimestamp(a.invoicedAt)).valueOf())

    setFilteredInvoicedJobs(filteredJobs)

    // Calculate full invoiced amount (sell)
    const totalSell = filteredJobs.reduce((sum, job) => {
      if (job.status === "Invoiced" || (job.status.includes("Paid") && job.services)) {
        // Job services sell total
        const jobServicesSell = (job.services || []).reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0)
        // Approved change orders sell total
        const changeOrdersSell = (jobChangeOrders?.[job.id] || [])
          .filter(co => co.status?.toLowerCase() === "approved")
          .reduce((sum, co) => sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0), 0)
        return sum + jobServicesSell + changeOrdersSell
      }
      return sum
    }, 0)

    // Calculate gross profit
    const grossProfit = filteredJobs.reduce((sum, job) => {
      if (job.status === "Invoiced" || (job.status.includes("Paid") && job.services)) {
        const jobServicesProfit = (job.services || []).reduce(
          (sum, service) => sum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
          0,
        )
        const changeOrdersProfit = (jobChangeOrders?.[job.id] || [])
          .filter(co => co.approvedBy)
          .reduce(
            (sum, co) =>
              sum + (co.services || []).reduce((coSum, service) => coSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1), 0),
            0,
          )
        return sum + jobServicesProfit + changeOrdersProfit
      }
      return sum
    }, 0)

    return {
      total: totalSell,
      profit: grossProfit,
    }
  }

  // Update totals when time range changes
  useEffect(() => {
    if (jobs.length) {
      const { total, profit } = calculateTotalsForPeriod(jobs)
      setInvoicedTotal(total)
      setGrossProfit(profit)
      const adminCosts = profit * (adminCostPercentage / 100)
      setAdminCosts(adminCosts)
      setNetIncome(profit - adminCosts)
    }
  }, [jobs, invoicedTimeRange, invoicedStartDate, invoicedEndDate, adminCostPercentage])

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Job Status Overview",
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || ""
            const value = context.raw
            const total = context.dataset.data.reduce((a, b) => a + b, 0)
            const percentage = Math.round((value / total) * 100)
            return `${label}: ${value} (${percentage}%)`
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
        suggestedMax: 10,
        afterDataLimits: scale => {
          const max = scale.max
          scale.max = Math.ceil(max / 10) * 10
        },
      },
    },
  }

  const handleSlackDisconnect = () => {
    // Remove the token from localStorage
    localStorage.removeItem("slack_token")
    // Close the drawer
    setIsSlackDrawerOpen(false)
    // Redirect to Slack's app management page to revoke access
    window.open("https://api.slack.com/apps", "_blank")
  }

  // Add CSV export function
  const exportToCSV = (data, filename) => {
    if (!data || data.length === 0) return

    // Get headers from the first row
    const headers = Object.keys(data[0])

    // Convert data to CSV format
    const csvContent = [
      headers.join(","), // Header row
      ...data.map(row =>
        headers
          .map(header => {
            const value = row[header]
            // Handle special cases
            if (value === null || value === undefined) return ""
            if (typeof value === "object") return JSON.stringify(value)
            // Escape commas and quotes
            return `"${String(value).replace(/"/g, '""')}"`
          })
          .join(","),
      ),
    ].join("\n")

    // Create and trigger download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `${filename}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Helper function to get widget props
  const getWidgetProps = widget => {
    const baseProps = {
      jobs,
      opportunities,
      leads,
      filteredJobs,
      filteredOpportunities,
      filteredLeads,
      filteredInvoicedJobs,
      jobChangeOrders,
      jobStatuses,
      opportunityStatuses,
      leadStatuses,
      statusColors,
      searchQuery,
      statusFilter,
      startDate,
      endDate,
      invoicedTotal,
      grossProfit,
      adminCosts,
      netIncome,
      adminCostPercentage,
      invoicedTimeRange,
      invoicedStartDate,
      invoicedEndDate,
      jobChartData,
      opportunityChartData,
      leadChartData,
      options,
      getDateFromTimestamp,
      exportToCSV,
      onSearchChange: setSearchQuery,
      onStatusFilterChange: setStatusFilter,
      onStartDateChange: setStartDate,
      onEndDateChange: setEndDate,
      onTimeRangeChange: setInvoicedTimeRange,
      onInvoicedStartDateChange: setInvoicedStartDate,
      onInvoicedEndDateChange: setInvoicedEndDate,
      onAdminCostPercentageChange: setAdminCostPercentage,
      onStatusChange: handleStatusChange,
      onDueDateChange: handleDueDateChange,
    }

    return baseProps
  }

  return (
    <ProtectedRoute allowedRoles={["user", "admin"]}>
      <div className="flex min-h-screen bg-gray-50">
        {/* Main Content */}
        <div className="flex-1 p-6">
          {/* Loading states */}
          {(isLoading || widgetsLoading) && <div className="fixed top-0 left-0 right-0 bg-blue-500 text-white p-2 text-center">Loading data...</div>}

          {/* Dashboard Header with Widget Management */}
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <button
              onClick={() => setIsWidgetMenuOpen(true)}
              className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              <FaCog size={16} />
              Manage Widgets
            </button>
          </div>

          {/* Widget Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-6">
            {enabledWidgets
              .filter(widget => !widget.adminOnly || admin)
              .map(widget => (
                <WidgetContainer key={widget.id} widget={widget} onRemove={removeWidget}>
                  {renderWidget(widget, getWidgetProps(widget))}
                </WidgetContainer>
              ))}
          </div>

          {/* Widget Management Menu */}
          <WidgetMenu
            widgets={enabledWidgets}
            availableWidgetTypes={availableWidgetTypes}
            onAddWidget={addWidget}
            onToggleWidget={toggleWidget}
            isOpen={isWidgetMenuOpen}
            onClose={() => setIsWidgetMenuOpen(false)}
          />
        </div>
      </div>
    </ProtectedRoute>
  )
}
