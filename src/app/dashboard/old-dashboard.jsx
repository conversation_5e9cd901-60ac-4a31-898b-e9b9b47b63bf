"use client"

import { useEffect, useState } from "react"
import { db } from "../../firebase"
import { collection, getDocs, doc, getDoc, query, where, getDocsFromCache } from "firebase/firestore"
import ProtectedRoute from "@/components/ProtectedRoute"
import { Bar } from "react-chartjs-2"
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js"
import { useSelector, useDispatch } from "react-redux"
import { useUser } from "@/hooks/useUser"
import StatusPickerWithColor from "@/components/StatusPickerWithColor"
import DatePicker from "@/components/DatePicker"
import SlackChat from "@/components/SlackChat"
import { IoChatbubbleEllipses } from "react-icons/io5"
import { FaChevronRight, FaFileExport } from "react-icons/fa"
import dayjs from "dayjs"
import isSameOrBefore from "dayjs/plugin/isSameOrBefore"
import isSameOrAfter from "dayjs/plugin/isSameOrAfter"
import quarterOfYear from "dayjs/plugin/quarterOfYear"
import { setStatusColors } from "@/slices/statusColorsSlice"

// Register dayjs plugins
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(quarterOfYear)

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("jobs")
  const [isSlackDrawerOpen, setIsSlackDrawerOpen] = useState(false)
  const [newMessageCount, setNewMessageCount] = useState(0)
  const [jobs, setJobs] = useState([])
  const [opportunities, setOpportunities] = useState([])
  const [leads, setLeads] = useState([])
  const [filteredJobs, setFilteredJobs] = useState([])
  const [filteredOpportunities, setFilteredOpportunities] = useState([])
  const [filteredLeads, setFilteredLeads] = useState([])
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("All")
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")
  const [jobChartData, setJobChartData] = useState({})
  const [opportunityChartData, setOpportunityChartData] = useState({})
  const [leadChartData, setLeadChartData] = useState({})
  const [invoicedTotal, setInvoicedTotal] = useState(0)
  const [adminCosts, setAdminCosts] = useState(0)
  const [netIncome, setNetIncome] = useState(0)
  const [adminCostPercentage, setAdminCostPercentage] = useState(30)
  const [jobStatuses, setJobStatuses] = useState([])
  const [opportunityStatuses, setOpportunityStatuses] = useState([])
  const [leadStatuses, setLeadStatuses] = useState([])
  const [invoicedTimeRange, setInvoicedTimeRange] = useState("ytd")
  const [invoicedStartDate, setInvoicedStartDate] = useState("")
  const [invoicedEndDate, setInvoicedEndDate] = useState("")
  const [filteredInvoicedJobs, setFilteredInvoicedJobs] = useState([])
  const [commissionTimeRange, setCommissionTimeRange] = useState("7days")
  const [commissionStartDate, setCommissionStartDate] = useState("")
  const [commissionEndDate, setCommissionEndDate] = useState("")
  const [jobChangeOrders, setJobChangeOrders] = useState({})
  const { companyId } = useSelector(state => state.company)
  const { admin } = useUser()
  const { user } = useSelector(state => state.auth)
  const dispatch = useDispatch()
  const statusColors = useSelector(state => state.statusColors)

  // Add new state for caching
  const [isLoading, setIsLoading] = useState(true)
  const [lastFetchTime, setLastFetchTime] = useState(null)
  const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes in milliseconds

  // Add grossProfit state
  const [grossProfit, setGrossProfit] = useState(0)

  // Optimized fetch function for all data
  const fetchAllData = async () => {
    if (!companyId || !user?.id) return

    try {
      setIsLoading(true)
      const now = Date.now()

      // Check if we can use cached data
      if (lastFetchTime && now - lastFetchTime < CACHE_DURATION) {
        console.log("Using cached data")
        setIsLoading(false)
        return
      }

      // Batch fetch all collections
      const [jobsSnapshot, opportunitiesSnapshot, leadsSnapshot, statusesDoc, colorsDoc] = await Promise.all([
        getDocs(collection(db, `companies/${companyId}/jobs`)),
        getDocs(collection(db, `companies/${companyId}/opportunities`)),
        getDocs(collection(db, `companies/${companyId}/leads`)),
        getDoc(doc(db, `companies/${companyId}/settings`, "statuses")),
        getDoc(doc(db, `companies/${companyId}/settings`, "statusColors")),
      ])

      // Process jobs data
      const jobList = jobsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }))

      // Filter jobs based on admin status
      const filteredJobList = admin ? jobList : jobList.filter(job => job.assignedTo === user.id)

      // Batch fetch change orders for all jobs
      const changeOrdersPromises = filteredJobList.map(job => getDocs(collection(db, `companies/${companyId}/jobs/${job.id}/changeOrders`)))
      const changeOrdersSnapshots = await Promise.all(changeOrdersPromises)

      // Process change orders
      const changeOrdersMap = {}
      changeOrdersSnapshots.forEach((snapshot, index) => {
        const jobId = filteredJobList[index].id
        changeOrdersMap[jobId] = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))
      })

      // Process opportunities
      const opportunityList = opportunitiesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }))
      const filteredOpportunityList = opportunityList.filter(opportunity => opportunity.assignedTo === user.id)

      // Process leads
      const leadList = leadsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }))
      const filteredLeadList = leadList.filter(lead => lead.assignedTo === user.id)

      // Process statuses and colors
      if (statusesDoc.exists()) {
        const data = statusesDoc.data()
        setJobStatuses(data.jobs || [])
        setOpportunityStatuses(data.opportunities || [])
        setLeadStatuses(data.leads || [])
      }
      if (colorsDoc.exists()) {
        dispatch(setStatusColors(colorsDoc.data()))
      }

      // Update all state at once
      setJobs(filteredJobList)
      setFilteredJobs(filteredJobList)
      setOpportunities(filteredOpportunityList)
      setFilteredOpportunities(filteredOpportunityList)
      setLeads(filteredLeadList)
      setFilteredLeads(filteredLeadList)
      setJobChangeOrders(changeOrdersMap)

      // Calculate initial totals
      const { total, profit } = calculateTotalsForPeriod(filteredJobList)
      setInvoicedTotal(total)
      setGrossProfit(profit)
      const adminCosts = profit * (adminCostPercentage / 100)
      setAdminCosts(adminCosts)
      setNetIncome(profit - adminCosts)

      // Update last fetch time
      setLastFetchTime(now)
    } catch (error) {
      console.error("Error fetching data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  // Replace multiple useEffects with a single one for initial data fetch
  useEffect(() => {
    fetchAllData()
  }, [companyId, user?.id, admin])

  // Add refresh function
  const handleRefresh = () => {
    setLastFetchTime(null) // Force refresh by clearing cache
    fetchAllData()
  }

  // Update the chart data when jobs or filters change
  useEffect(() => {
    const actualStatuses = [...new Set(jobs.map(job => job.status))]

    // Jobs Chart Data
    const jobStatusCounts = jobs.reduce((acc, job) => {
      if (!job.status) {
        return acc
      }
      acc[job.status] = (acc[job.status] || 0) + 1
      return acc
    }, {})

    // Now ensure all statuses from jobStatuses are in the counts
    const finalJobStatusCounts = jobStatuses.reduce((acc, status) => {
      acc[status] = jobStatusCounts[status] || 0
      return acc
    }, {})

    // Opportunities Chart Data
    const opportunityStatusCounts = opportunities.reduce(
      (acc, opportunity) => {
        acc[opportunity.status] = (acc[opportunity.status] || 0) + 1
        return acc
      },
      opportunityStatuses.reduce((acc, status) => {
        acc[status] = 0
        return acc
      }, {}),
    )

    // Leads Chart Data
    const leadStatusCounts = leads.reduce(
      (acc, lead) => {
        acc[lead.status] = (acc[lead.status] || 0) + 1
        return acc
      },
      leadStatuses.reduce((acc, status) => {
        acc[status] = 0
        return acc
      }, {}),
    )

    setJobChartData({
      labels: Object.keys(finalJobStatusCounts),
      datasets: [
        {
          label: "Job Status Overview",
          data: Object.values(finalJobStatusCounts),
          backgroundColor: Object.keys(finalJobStatusCounts).map(status => statusColors.jobs?.[status] || "#6b7280"),
        },
      ],
    })

    setOpportunityChartData({
      labels: Object.keys(opportunityStatusCounts),
      datasets: [
        {
          label: "Opportunity Status Overview",
          data: Object.values(opportunityStatusCounts),
          backgroundColor: Object.keys(opportunityStatusCounts).map(status => statusColors.opportunities?.[status] || "#6b7280"),
        },
      ],
    })

    setLeadChartData({
      labels: Object.keys(leadStatusCounts),
      datasets: [
        {
          label: "Lead Status Overview",
          data: Object.values(leadStatusCounts),
          backgroundColor: Object.keys(leadStatusCounts).map(status => statusColors.leads?.[status] || "#6b7280"),
        },
      ],
    })
  }, [jobs, opportunities, leads, jobStatuses, opportunityStatuses, leadStatuses, statusColors])

  // Filter Data
  useEffect(() => {
    let updatedJobs = jobs
    let updatedOpportunities = opportunities
    let updatedLeads = leads

    // Apply Search Query
    if (searchQuery) {
      updatedJobs = updatedJobs.filter(job => job.title?.toLowerCase().includes(searchQuery.toLowerCase()))
      updatedOpportunities = updatedOpportunities.filter(opportunity => opportunity.title?.toLowerCase().includes(searchQuery.toLowerCase()))
      updatedLeads = updatedLeads.filter(lead => lead.title?.toLowerCase().includes(searchQuery.toLowerCase()))
    }

    // Apply Status Filter
    if (statusFilter !== "All") {
      updatedJobs = updatedJobs.filter(job => job.status === statusFilter)
      updatedOpportunities = updatedOpportunities.filter(opportunity => opportunity.status === statusFilter)
      updatedLeads = updatedLeads.filter(lead => lead.status === statusFilter)
    }

    // Apply Date Filters
    if (startDate) {
      updatedJobs = updatedJobs.filter(job => new Date(getDateFromTimestamp(job.createdAt)) >= new Date(startDate))
      updatedOpportunities = updatedOpportunities.filter(opportunity => new Date(getDateFromTimestamp(opportunity.createdAt)) >= new Date(startDate))
      updatedLeads = updatedLeads.filter(lead => new Date(getDateFromTimestamp(lead.createdAt)) >= new Date(startDate))
    }
    if (endDate) {
      updatedJobs = updatedJobs.filter(job => new Date(getDateFromTimestamp(job.createdAt)) <= new Date(endDate))
      updatedOpportunities = updatedOpportunities.filter(opportunity => new Date(getDateFromTimestamp(opportunity.createdAt)) <= new Date(endDate))
      updatedLeads = updatedLeads.filter(lead => new Date(getDateFromTimestamp(lead.createdAt)) <= new Date(endDate))
    }

    setFilteredJobs(updatedJobs)
    setFilteredOpportunities(updatedOpportunities)
    setFilteredLeads(updatedLeads)
  }, [searchQuery, statusFilter, startDate, endDate, jobs, opportunities, leads])

  // Helper function to safely convert dates
  const getDateFromTimestamp = timestamp => {
    if (!timestamp) return null
    if (typeof timestamp.toDate === "function") {
      return timestamp.toDate()
    }
    if (timestamp instanceof Date) {
      return timestamp
    }
    return new Date(timestamp)
  }

  // Filter invoiced jobs based on time range
  useEffect(() => {
    if (!jobs.length) {
      return
    }

    const now = new Date()
    let startDate = new Date()
    let endDate = new Date()

    switch (invoicedTimeRange) {
      case "7days":
        startDate.setDate(now.getDate() - 7)
        break
      case "30days":
        startDate.setDate(now.getDate() - 30)
        break
      case "quarter":
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
        break
      case "custom":
        if (invoicedStartDate && invoicedEndDate) {
          startDate = new Date(invoicedStartDate)
          endDate = new Date(invoicedEndDate)
        }
        break
    }

    const filteredJobs = jobs
      .filter(job => {
        if (!job.invoicedAt) {
          return false
        }
        const invoicedDate = getDateFromTimestamp(job.invoicedAt)
        const isInRange = invoicedDate >= startDate && invoicedDate <= endDate
        return isInRange
      })
      .sort((a, b) => getDateFromTimestamp(b.invoicedAt) - getDateFromTimestamp(a.invoicedAt))

    // Update filtered invoiced jobs for the table
    setFilteredInvoicedJobs(filteredJobs)

    const total = filteredJobs.reduce((sum, job) => {
      if (job.status === "Invoiced" || (job.status.includes("Paid") && job.services)) {
        const jobTotal = job.services.reduce((serviceSum, service) => {
          return serviceSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1)
        }, 0)
        console.log("Job total:", { id: job.id, total: jobTotal })
        return sum + jobTotal
      }
      return sum
    }, 0)

    setInvoicedTotal(total)
    setAdminCosts(total * (adminCostPercentage / 100))
    setNetIncome(total - total * (adminCostPercentage / 100))
  }, [jobs, invoicedTimeRange, invoicedStartDate, invoicedEndDate, adminCostPercentage])

  // Filter jobs based on commission time range
  useEffect(() => {
    if (!jobs.length) {
      return
    }

    const now = new Date()
    let startDate = new Date()
    let endDate = new Date()

    switch (commissionTimeRange) {
      case "7days":
        startDate.setDate(now.getDate() - 7)
        break
      case "30days":
        startDate.setDate(now.getDate() - 30)
        break
      case "quarter":
        startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1)
        break
      case "custom":
        if (commissionStartDate && commissionEndDate) {
          startDate = new Date(commissionStartDate)
          endDate = new Date(commissionEndDate)
        }
        break
    }

    const filtered = jobs.filter(job => {
      if (job.status !== "Invoiced" || !job.invoicedAt) return false
      const invoicedDate = getDateFromTimestamp(job.invoicedAt)
      return invoicedDate >= startDate && invoicedDate <= endDate
    })

    setFilteredInvoicedJobs(filtered)
  }, [jobs, commissionTimeRange, commissionStartDate, commissionEndDate])

  // Update the status of a job
  const handleStatusChange = async (jobId, newStatus) => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await updateDoc(docRef, { status: newStatus })

      // Update local state
      setJobs(prevJobs => prevJobs.map(job => (job.id === jobId ? { ...job, status: newStatus } : job)))
    } catch (error) {
      console.error("Error updating status:", error.message)
    }
  }

  // Delete a job
  const handleDelete = async jobId => {
    const confirmDelete = window.confirm("Are you sure you want to delete this job?")
    if (!confirmDelete) return

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await deleteDoc(docRef)

      // Update the local state
      setJobs(prevJobs => prevJobs.filter(job => job.id !== jobId))
      setFilteredJobs(prevFilteredJobs => prevFilteredJobs.filter(job => job.id !== jobId))
    } catch (error) {
      console.error("Error deleting job:", error.message)
    }
  }

  // Update the due date of a job
  const handleDueDateChange = async (jobId, newDueDate) => {
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      // Convert the date string to a Firestore timestamp
      const timestamp = newDueDate ? new Date(newDueDate) : null
      await updateDoc(docRef, { dueDate: timestamp })

      // Update local state
      setJobs(prevJobs => prevJobs.map(job => (job.id === jobId ? { ...job, dueDate: timestamp } : job)))
    } catch (error) {
      console.error("Error updating due date:", error.message)
    }
  }

  // Reset custom date range when time range changes
  useEffect(() => {
    if (invoicedTimeRange !== "custom") {
      setInvoicedStartDate("")
      setInvoicedEndDate("")
    }
    // Clear filtered jobs when time range changes
    setFilteredInvoicedJobs([])
  }, [invoicedTimeRange])

  const calculateTotalsForPeriod = jobs => {
    const now = dayjs()
    let startDate
    let endDate = now.endOf("day")

    switch (invoicedTimeRange) {
      case "ytd":
        startDate = now.startOf("year")
        break
      case "7days":
        startDate = now.subtract(7, "day").startOf("day")
        break
      case "30days":
        startDate = now.subtract(30, "day").startOf("day")
        break
      case "quarter":
        const currentQuarter = now.quarter()
        startDate = dayjs().quarter(currentQuarter).startOf("quarter")
        endDate = dayjs().quarter(currentQuarter).endOf("quarter")
        break
      case "custom":
        if (invoicedStartDate && invoicedEndDate) {
          startDate = dayjs(invoicedStartDate).startOf("day")
          endDate = dayjs(invoicedEndDate).endOf("day")
        }
        break
    }

    const filteredJobs = jobs
      .filter(job => {
        if (job.status === "Invoiced" || job.status.includes("Paid")) {
          const invoicedDate = dayjs(getDateFromTimestamp(job.invoicedAt))
          return invoicedDate.isSameOrAfter(startDate) && invoicedDate.isSameOrBefore(endDate)
        }
        return false
      })
      .sort((a, b) => dayjs(getDateFromTimestamp(b.invoicedAt)).valueOf() - dayjs(getDateFromTimestamp(a.invoicedAt)).valueOf())

    setFilteredInvoicedJobs(filteredJobs)

    // Calculate full invoiced amount (sell)
    const totalSell = filteredJobs.reduce((sum, job) => {
      if (job.status === "Invoiced" || (job.status.includes("Paid") && job.services)) {
        // Job services sell total
        const jobServicesSell = (job.services || []).reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0)
        // Approved change orders sell total
        const changeOrdersSell = (jobChangeOrders?.[job.id] || [])
          .filter(co => co.status?.toLowerCase() === "approved")
          .reduce((sum, co) => sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0), 0)
        return sum + jobServicesSell + changeOrdersSell
      }
      return sum
    }, 0)

    // Calculate gross profit
    const grossProfit = filteredJobs.reduce((sum, job) => {
      if (job.status === "Invoiced" || (job.status.includes("Paid") && job.services)) {
        const jobServicesProfit = (job.services || []).reduce(
          (sum, service) => sum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
          0,
        )
        const changeOrdersProfit = (jobChangeOrders?.[job.id] || [])
          .filter(co => co.approvedBy)
          .reduce(
            (sum, co) =>
              sum + (co.services || []).reduce((coSum, service) => coSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1), 0),
            0,
          )
        return sum + jobServicesProfit + changeOrdersProfit
      }
      return sum
    }, 0)

    return {
      total: totalSell,
      profit: grossProfit,
    }
  }

  // Update totals when time range changes
  useEffect(() => {
    if (jobs.length) {
      const { total, profit } = calculateTotalsForPeriod(jobs)
      setInvoicedTotal(total)
      setGrossProfit(profit)
      const adminCosts = profit * (adminCostPercentage / 100)
      setAdminCosts(adminCosts)
      setNetIncome(profit - adminCosts)
    }
  }, [jobs, invoicedTimeRange, invoicedStartDate, invoicedEndDate, adminCostPercentage])

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Job Status Overview",
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const label = context.dataset.label || ""
            const value = context.raw
            const total = context.dataset.data.reduce((a, b) => a + b, 0)
            const percentage = Math.round((value / total) * 100)
            return `${label}: ${value} (${percentage}%)`
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
        suggestedMax: 10,
        afterDataLimits: scale => {
          const max = scale.max
          scale.max = Math.ceil(max / 10) * 10
        },
      },
    },
  }

  const handleSlackDisconnect = () => {
    // Remove the token from localStorage
    localStorage.removeItem("slack_token")
    // Close the drawer
    setIsSlackDrawerOpen(false)
    // Redirect to Slack's app management page to revoke access
    window.open("https://api.slack.com/apps", "_blank")
  }

  // Add CSV export function
  const exportToCSV = (data, filename) => {
    if (!data || data.length === 0) return

    // Get headers from the first row
    const headers = Object.keys(data[0])

    // Convert data to CSV format
    const csvContent = [
      headers.join(","), // Header row
      ...data.map(row =>
        headers
          .map(header => {
            const value = row[header]
            // Handle special cases
            if (value === null || value === undefined) return ""
            if (typeof value === "object") return JSON.stringify(value)
            // Escape commas and quotes
            return `"${String(value).replace(/"/g, '""')}"`
          })
          .join(","),
      ),
    ].join("\n")

    // Create and trigger download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `${filename}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <ProtectedRoute allowedRoles={["user", "admin"]}>
      <div className="flex min-h-screen bg-gray-50">
        {/* Main Content */}
        <div className="flex-1 p-6">
          {/* Add loading state and refresh button */}
          {isLoading && <div className="fixed top-0 left-0 right-0 bg-blue-500 text-white p-2 text-center">Loading data...</div>}

          {/* Add refresh button to the top of the dashboard */}
          {/* <div className="flex justify-end mb-4">
            <button onClick={handleRefresh} className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
              Refresh Data
            </button>
          </div> */}

          {/* Accounting Summary */}
          <section className="mb-6 bg-white p-4 rounded shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold text-gray-900">Accounting Summary</h2>
              <div className="flex items-center gap-4">
                <select
                  value={invoicedTimeRange}
                  onChange={e => setInvoicedTimeRange(e.target.value)}
                  className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                >
                  <option value="ytd">Year to Date</option>
                  <option value="7days">Last 7 Days</option>
                  <option value="30days">Last 30 Days</option>
                  <option value="quarter">This Quarter</option>
                  <option value="custom">Custom Range</option>
                </select>
                {invoicedTimeRange === "custom" && (
                  <div className="flex items-center gap-2">
                    <input
                      type="date"
                      value={invoicedStartDate}
                      onChange={e => setInvoicedStartDate(e.target.value)}
                      className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                    />
                    <span className="text-gray-900">to</span>
                    <input
                      type="date"
                      value={invoicedEndDate}
                      onChange={e => setInvoicedEndDate(e.target.value)}
                      className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                    />
                  </div>
                )}
                {admin && (
                  <div className="flex items-center gap-2">
                    <label className="text-sm font-medium text-gray-600">Admin Cost %:</label>
                    <select
                      value={adminCostPercentage}
                      onChange={e => setAdminCostPercentage(Number(e.target.value))}
                      className="border border-gray-300 rounded p-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                    >
                      {[5, 10, 15, 20, 25, 30].map(percent => (
                        <option key={percent} value={percent}>
                          {percent}%
                        </option>
                      ))}
                    </select>
                  </div>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {admin ? (
                <>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="text-sm font-medium text-gray-600">Total Invoiced</h3>
                    <p className="text-2xl font-bold text-gray-900">
                      ${invoicedTotal.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h3 className="text-sm font-medium text-gray-600">Gross Profit</h3>
                    <p className="text-2xl font-bold text-gray-900">
                      ${grossProfit.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </p>
                  </div>
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h3 className="text-sm font-medium text-gray-600">Administrative Costs ({adminCostPercentage}%)</h3>
                    <p className="text-2xl font-bold text-gray-900">
                      ${adminCosts.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <h3 className="text-sm font-medium text-gray-600">Net Income</h3>
                    <p className="text-2xl font-bold text-gray-900">
                      ${netIncome.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </p>
                  </div>
                </>
              ) : (
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-sm font-medium text-gray-600">Your Commission ({Math.round((user?.commissionRate || 0) * 100)}%)</h3>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">
                    ${(invoicedTotal * (user?.commissionRate || 0)).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </p>
                </div>
              )}
            </div>
          </section>

          {/* Completed and Paid Jobs Summary */}
          <section className="mb-6 bg-white p-4 rounded shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold text-gray-900">Completed and Paid Jobs</h2>
              <button
                onClick={() => {
                  const exportData = jobs
                    .filter(job => job.status === "Completed" || (job.status && job.status.toLowerCase().includes("paid")))
                    .map(job => ({
                      "Work Order ID": job.workOrderId || "",
                      "Job ID": job.displayId || "",
                      Title: job.name || "",
                      Customer: job.customer?.name || "",
                      Location: job.location || "",
                      Status: job.status || "",
                      "Completion Date": job.completedAt ? getDateFromTimestamp(job.completedAt)?.toLocaleDateString() : "",
                      "Total Amount": (() => {
                        const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0
                        const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
                          .filter(co => co.status?.toLowerCase() === "approved")
                          .reduce(
                            (sum, co) =>
                              sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
                            0,
                          )
                        return (servicesTotal + changeOrdersTotal).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })
                      })(),
                    }))
                  exportToCSV(exportData, "completed-paid-jobs")
                }}
                className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
              >
                <FaFileExport /> Export CSV
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border p-2 text-gray-900">Work Order ID</th>
                    <th className="border p-2 text-gray-900">Job ID</th>
                    <th className="border p-2 text-gray-900">Title</th>
                    <th className="border p-2 text-gray-900">Customer</th>
                    <th className="border p-2 text-gray-900">Location</th>
                    <th className="border p-2 text-gray-900">Status</th>
                    <th className="border p-2 text-gray-900">Completion Date</th>
                    <th className="border p-2 text-gray-900">Total Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {jobs
                    .filter(job => job.status === "Completed" || (job.status && job.status.toLowerCase().includes("paid")))
                    .map(job => (
                      <tr key={job.id}>
                        <td className="border p-2 text-gray-800">{job.workOrderId}</td>
                        <td className="border p-2 text-gray-800">
                          <a href={`/jobs/${job.id}`} className="text-blue-500 hover:text-blue-700 underline hover:no-underline">
                            {job.displayId}
                          </a>
                        </td>
                        <td className="border p-2 text-gray-800">{job.name}</td>
                        <td className="border p-2 text-gray-800">{job.customer?.name}</td>
                        <td className="border p-2 text-gray-800">{job.location}</td>
                        <td className="border p-2 text-gray-800">{job.status}</td>
                        <td className="border p-2 text-gray-800">
                          {job.completedAt ? getDateFromTimestamp(job.completedAt)?.toLocaleDateString() : "-"}
                        </td>
                        <td className="border p-2 text-gray-800">
                          $
                          {(() => {
                            // Calculate total from job services
                            const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0

                            // Calculate total from approved change orders
                            const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
                              .filter(co => co.status?.toLowerCase() === "approved")
                              .reduce(
                                (sum, co) =>
                                  sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
                                0,
                              )

                            // Return combined total formatted with commas
                            return (servicesTotal + changeOrdersTotal).toLocaleString("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })
                          })()}
                        </td>
                      </tr>
                    ))}
                  {/* Add subtotal row */}
                  <tr className="bg-gray-50 font-semibold">
                    <td colSpan="7" className="border p-2 text-gray-800 text-right">
                      Subtotal:
                    </td>
                    <td className="border p-2 text-gray-800">
                      $
                      {(() => {
                        const total = jobs
                          .filter(job => job.status === "Completed" || (job.status && job.status.toLowerCase().includes("paid")))
                          .reduce((sum, job) => {
                            // Calculate total from job services
                            const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0

                            // Calculate total from approved change orders
                            const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
                              .filter(co => co.status?.toLowerCase() === "approved")
                              .reduce(
                                (sum, co) =>
                                  sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
                                0,
                              )

                            return sum + servicesTotal + changeOrdersTotal
                          }, 0)

                        return total.toLocaleString("en-US", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })
                      })()}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>

          {/* Invoiced Jobs Summary */}
          {admin && (
            <section className="mb-6 bg-white p-4 rounded shadow-md">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-bold text-gray-900">Invoiced Jobs Summary</h2>
                <button
                  onClick={() => {
                    const exportData = filteredInvoicedJobs.map(job => ({
                      "Job ID": job.displayId || "",
                      Title: job.name || "",
                      Customer: job.customer?.name || "",
                      "Invoiced Date": getDateFromTimestamp(job.invoicedAt)?.toLocaleDateString() || "",
                      "Total Amount": (() => {
                        const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0
                        const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
                          .filter(co => co.status?.toLowerCase() === "approved")
                          .reduce(
                            (sum, co) =>
                              sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
                            0,
                          )
                        return (servicesTotal + changeOrdersTotal).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })
                      })(),
                      Profit: (() => {
                        const jobServicesProfit = (job.services || []).reduce(
                          (sum, service) => sum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
                          0,
                        )
                        const changeOrdersProfit = (jobChangeOrders?.[job.id] || [])
                          .filter(co => co.approvedBy)
                          .reduce(
                            (sum, co) =>
                              sum +
                              (co.services || []).reduce(
                                (coSum, service) => coSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
                                0,
                              ),
                            0,
                          )
                        return (jobServicesProfit + changeOrdersProfit).toLocaleString("en-US", {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })
                      })(),
                    }))
                    exportToCSV(exportData, "invoiced-jobs")
                  }}
                  className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                >
                  <FaFileExport /> Export CSV
                </button>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="border p-2 text-gray-900">Job ID</th>
                      <th className="border p-2 text-gray-900">Title</th>
                      <th className="border p-2 text-gray-900">Customer</th>
                      <th className="border p-2 text-gray-900">Invoiced Date</th>
                      <th className="border p-2 text-gray-900">Total Amount</th>
                      <th className="border p-2 text-gray-900">Profit</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredInvoicedJobs.map(job => (
                      <tr key={job.id}>
                        <td className="border p-2 text-gray-800">
                          <a href={`/jobs/${job.id}`} className="text-blue-500 hover:text-blue-700 underline hover:no-underline">
                            {job.displayId}
                          </a>
                        </td>
                        <td className="border p-2 text-gray-800">{job.name}</td>
                        <td className="border p-2 text-gray-800">{job.customer?.name}</td>
                        <td className="border p-2 text-gray-800">{getDateFromTimestamp(job.invoicedAt)?.toLocaleDateString()}</td>
                        <td className="border p-2 text-gray-800">
                          $
                          {(() => {
                            // Calculate total from job services
                            const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0

                            // Calculate total from approved change orders
                            const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
                              .filter(co => co.status?.toLowerCase() === "approved")
                              .reduce(
                                (sum, co) =>
                                  sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
                                0,
                              )

                            // Return combined total formatted with commas
                            return (servicesTotal + changeOrdersTotal).toLocaleString("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })
                          })()}
                        </td>
                        <td className="border p-2 text-gray-800">
                          $
                          {(() => {
                            // Calculate profit from job services
                            const jobServicesProfit = (job.services || []).reduce(
                              (sum, service) => sum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
                              0,
                            )

                            // Calculate profit from approved change orders
                            const changeOrdersProfit = (jobChangeOrders?.[job.id] || [])
                              .filter(co => co.approvedBy)
                              .reduce(
                                (sum, co) =>
                                  sum +
                                  (co.services || []).reduce(
                                    (coSum, service) => coSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
                                    0,
                                  ),
                                0,
                              )

                            // Add both profits together and format
                            return (jobServicesProfit + changeOrdersProfit).toLocaleString("en-US", {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })
                          })()}
                        </td>
                      </tr>
                    ))}
                    {/* Add subtotal row */}
                    <tr className="bg-gray-50 font-semibold">
                      <td colSpan="4" className="border p-2 text-gray-800 text-right">
                        Subtotal:
                      </td>
                      <td className="border p-2 text-gray-800">
                        $
                        {(() => {
                          const totalAmount = filteredInvoicedJobs.reduce((sum, job) => {
                            const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0
                            const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
                              .filter(co => co.status?.toLowerCase() === "approved")
                              .reduce(
                                (sum, co) =>
                                  sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
                                0,
                              )
                            return sum + servicesTotal + changeOrdersTotal
                          }, 0)

                          return totalAmount.toLocaleString("en-US", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })
                        })()}
                      </td>
                      <td className="border p-2 text-gray-800">
                        $
                        {(() => {
                          const totalProfit = filteredInvoicedJobs.reduce((sum, job) => {
                            const jobServicesProfit = (job.services || []).reduce(
                              (sum, service) => sum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
                              0,
                            )
                            const changeOrdersProfit = (jobChangeOrders?.[job.id] || [])
                              .filter(co => co.approvedBy)
                              .reduce(
                                (sum, co) =>
                                  sum +
                                  (co.services || []).reduce(
                                    (coSum, service) => coSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
                                    0,
                                  ),
                                0,
                              )
                            return sum + jobServicesProfit + changeOrdersProfit
                          }, 0)

                          return totalProfit.toLocaleString("en-US", {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })
                        })()}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </section>
          )}

          {/* Tabs */}
          <div className="mb-6">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab("jobs")}
                  className={`${
                    activeTab === "jobs"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  {admin ? "Jobs" : "My Jobs"}
                </button>
                <button
                  onClick={() => setActiveTab("opportunities")}
                  className={`${
                    activeTab === "opportunities"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  {admin ? "Opportunities" : "My Opportunities"}
                </button>
                <button
                  onClick={() => setActiveTab("leads")}
                  className={`${
                    activeTab === "leads"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  {admin ? "Leads" : "My Leads"}
                </button>
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {/* Jobs Tab */}
            {activeTab === "jobs" && (
              <>
                {/* Job Status Overview */}
                <section className="bg-white p-4 rounded shadow-md">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between">
                    <h2 className="text-lg font-bold mb-4 lg:mb-0 text-gray-900">Job Status Overview</h2>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-700">
                      {jobStatuses.map(status => (
                        <div key={status} className="flex items-center gap-2">
                          <span className={`block w-4 h-4 rounded-sm`} style={{ backgroundColor: statusColors.jobs?.[status] || "#6b7280" }}></span>
                          <span>{status}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="mt-4 h-80">
                    <Bar
                      data={jobChartData}
                      options={{
                        ...options,
                        maintainAspectRatio: false,
                        responsive: true,
                      }}
                    />
                  </div>
                </section>

                {/* Job Filters */}
                <section className="bg-white p-4 rounded shadow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Search by job title"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                  <select
                    value={statusFilter}
                    onChange={e => setStatusFilter(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  >
                    <option value="All">All Statuses</option>
                    {jobStatuses.map(status => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                  <input
                    type="date"
                    value={startDate}
                    onChange={e => setStartDate(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                  <input
                    type="date"
                    value={endDate}
                    onChange={e => setEndDate(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                </section>

                {/* Job Table */}
                <div className="bg-white p-4 rounded shadow">
                  <div className="flex justify-between items-center mb-4">
                    <h1 className="text-lg font-bold text-gray-900">My Jobs</h1>
                    <button
                      onClick={() => {
                        const exportData = filteredJobs.map(job => ({
                          "Job ID": job.displayId || "",
                          "Job Title": job.name || "",
                          Customer: job.customer?.name || "",
                          "Due Date": job.dueDate ? getDateFromTimestamp(job.dueDate)?.toLocaleDateString() : "",
                          Status: job.status || "",
                        }))
                        exportToCSV(exportData, "jobs")
                      }}
                      className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                    >
                      <FaFileExport /> Export CSV
                    </button>
                  </div>
                  {filteredJobs.length > 0 ? (
                    <table className="w-full border-collapse">
                      <thead>
                        <tr>
                          <th className="border p-2 text-gray-900 w-0 whitespace-nowrap">Job ID</th>
                          <th className="border p-2 text-gray-900 w-2/10">Job Title</th>
                          <th className="border p-2 text-gray-900 w-3/10">Customer</th>
                          <th className="border p-2 text-gray-900 w-2/12 whitespace-nowrap">Due Date</th>
                          <th className="border p-2 text-gray-900 w-2/12">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredJobs.map(job => (
                          <tr key={job.id}>
                            <td className="border p-2 text-gray-800 whitespace-nowrap">
                              <a href={`/jobs/${job.id}`} className="text-blue-500 hover:text-blue-700 underline hover:no-underline">
                                {job.displayId}
                              </a>
                            </td>
                            <td className="border p-2 text-gray-800">{job.name}</td>
                            <td className="border p-2 text-gray-800">{job.customer.name}</td>
                            <td className="border p-2 text-gray-800 whitespace-nowrap text-center">
                              <div className="flex justify-center">
                                <DatePicker
                                  value={job.dueDate ? getDateFromTimestamp(job.dueDate)?.toISOString().split("T")[0] : ""}
                                  onChange={newDate => handleDueDateChange(job.id, newDate)}
                                  isEditing={true}
                                />
                              </div>
                            </td>
                            <td className="border p-2">
                              <StatusPickerWithColor
                                type="jobs"
                                currentStatus={job?.status}
                                onStatusChange={newStatus => handleStatusChange(job.id, newStatus)}
                                className="w-full"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <p className="text-gray-600">No jobs found. Try adjusting your filters.</p>
                  )}
                </div>
              </>
            )}

            {/* Opportunities Tab */}
            {activeTab === "opportunities" && (
              <>
                {/* Opportunity Status Overview */}
                <section className="bg-white p-4 rounded shadow-md">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between">
                    <h2 className="text-lg font-bold mb-4 lg:mb-0 text-gray-900">Opportunity Status Overview</h2>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-700">
                      {opportunityStatuses.map(status => (
                        <div key={status} className="flex items-center gap-2">
                          <span
                            className={`block w-4 h-4 rounded-sm`}
                            style={{ backgroundColor: statusColors.opportunities?.[status] || "#6b7280" }}
                          ></span>
                          <span>{status}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="mt-4 h-80">
                    <Bar
                      data={opportunityChartData}
                      options={{
                        ...options,
                        maintainAspectRatio: false,
                        responsive: true,
                      }}
                    />
                  </div>
                </section>

                {/* Opportunity Filters */}
                <section className="bg-white p-4 rounded shadow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Search by opportunity title"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                  <select
                    value={statusFilter}
                    onChange={e => setStatusFilter(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  >
                    <option value="All">All Statuses</option>
                    {opportunityStatuses.map(status => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                  <input
                    type="date"
                    value={startDate}
                    onChange={e => setStartDate(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                  <input
                    type="date"
                    value={endDate}
                    onChange={e => setEndDate(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                </section>

                {/* Opportunity Table */}
                <div className="bg-white p-4 rounded shadow">
                  <div className="flex justify-between items-center mb-4">
                    <h1 className="text-lg font-bold text-gray-900">My Opportunities</h1>
                    <button
                      onClick={() => {
                        const exportData = filteredOpportunities.map(opportunity => ({
                          "Opportunity ID": opportunity.displayId || "",
                          Title: opportunity.name || "",
                          Customer: opportunity.customer?.name || "",
                          "Due Date": opportunity.dueDate ? getDateFromTimestamp(opportunity.dueDate)?.toLocaleDateString() : "",
                          Status: opportunity.status || "",
                        }))
                        exportToCSV(exportData, "opportunities")
                      }}
                      className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                    >
                      <FaFileExport /> Export CSV
                    </button>
                  </div>
                  {filteredOpportunities.length > 0 ? (
                    <table className="w-full border-collapse">
                      <thead>
                        <tr>
                          <th className="border p-2 text-gray-900 w-0 whitespace-nowrap">Opportunity ID</th>
                          <th className="border p-2 text-gray-900 w-2/10">Title</th>
                          <th className="border p-2 text-gray-900 w-3/10">Customer</th>
                          <th className="border p-2 text-gray-900 w-2/12 whitespace-nowrap">Due Date</th>
                          <th className="border p-2 text-gray-900 w-2/12">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredOpportunities.map(opportunity => (
                          <tr key={opportunity.id}>
                            <td className="border p-2 text-gray-800 whitespace-nowrap">
                              <a href={`/opportunities/${opportunity.id}`} className="text-blue-500 hover:text-blue-700 underline hover:no-underline">
                                {opportunity.displayId}
                              </a>
                            </td>
                            <td className="border p-2 text-gray-800">{opportunity.name}</td>
                            <td className="border p-2 text-gray-800">{opportunity.customer?.name}</td>
                            <td className="border p-2 text-gray-800 whitespace-nowrap text-center">
                              <div className="flex justify-center">
                                <DatePicker
                                  value={opportunity.dueDate ? getDateFromTimestamp(opportunity.dueDate)?.toISOString().split("T")[0] : ""}
                                  onChange={newDate => handleDueDateChange(opportunity.id, newDate)}
                                  isEditing={true}
                                />
                              </div>
                            </td>
                            <td className="border p-2">
                              <StatusPickerWithColor
                                type="opportunities"
                                currentStatus={opportunity.status}
                                onStatusChange={newStatus => handleStatusChange(opportunity.id, newStatus)}
                                className="w-full"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <p className="text-gray-600">No opportunities found. Try adjusting your filters.</p>
                  )}
                </div>
              </>
            )}

            {/* Leads Tab */}
            {activeTab === "leads" && (
              <>
                {/* Lead Status Overview */}
                <section className="bg-white p-4 rounded shadow-md">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between">
                    <h2 className="text-lg font-bold mb-4 lg:mb-0 text-gray-900">Lead Status Overview</h2>
                    <div className="flex flex-wrap gap-4 text-sm text-gray-700">
                      {leadStatuses.map(status => (
                        <div key={status} className="flex items-center gap-2">
                          <span className={`block w-4 h-4 rounded-sm`} style={{ backgroundColor: statusColors.leads?.[status] || "#6b7280" }}></span>
                          <span>{status}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="mt-4 h-80">
                    <Bar
                      data={leadChartData}
                      options={{
                        ...options,
                        maintainAspectRatio: false,
                        responsive: true,
                      }}
                    />
                  </div>
                </section>

                {/* Lead Filters */}
                <section className="bg-white p-4 rounded shadow grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Search by lead title"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                  <select
                    value={statusFilter}
                    onChange={e => setStatusFilter(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  >
                    <option value="All">All Statuses</option>
                    {leadStatuses.map(status => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>
                  <input
                    type="date"
                    value={startDate}
                    onChange={e => setStartDate(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                  <input
                    type="date"
                    value={endDate}
                    onChange={e => setEndDate(e.target.value)}
                    className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                </section>

                {/* Lead Table */}
                <div className="bg-white p-4 rounded shadow">
                  <div className="flex justify-between items-center mb-4">
                    <h1 className="text-lg font-bold text-gray-900">My Leads</h1>
                    <button
                      onClick={() => {
                        const exportData = filteredLeads.map(lead => ({
                          "Lead ID": lead.displayId || "",
                          Title: lead.name || "",
                          Customer: lead.customer?.name || "",
                          "Due Date": lead.dueDate ? getDateFromTimestamp(lead.dueDate)?.toLocaleDateString() : "",
                          Status: lead.status || "",
                        }))
                        exportToCSV(exportData, "leads")
                      }}
                      className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                    >
                      <FaFileExport /> Export CSV
                    </button>
                  </div>
                  {filteredLeads.length > 0 ? (
                    <table className="w-full border-collapse">
                      <thead>
                        <tr>
                          <th className="border p-2 text-gray-900 w-0 whitespace-nowrap">Lead ID</th>
                          <th className="border p-2 text-gray-900 w-2/10">Title</th>
                          <th className="border p-2 text-gray-900 w-3/10">Customer</th>
                          <th className="border p-2 text-gray-900 w-2/12 whitespace-nowrap">Due Date</th>
                          <th className="border p-2 text-gray-900 w-2/12">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredLeads.map(lead => (
                          <tr key={lead.id}>
                            <td className="border p-2 text-gray-800 whitespace-nowrap">
                              <a href={`/leads/${lead.id}`} className="text-blue-500 hover:text-blue-700 underline hover:no-underline">
                                {lead.displayId}
                              </a>
                            </td>
                            <td className="border p-2 text-gray-800">{lead.name}</td>
                            <td className="border p-2 text-gray-800">{lead.customer?.name}</td>
                            <td className="border p-2 text-gray-800 whitespace-nowrap text-center">
                              <div className="flex justify-center">
                                <DatePicker
                                  value={lead.dueDate ? getDateFromTimestamp(lead.dueDate)?.toISOString().split("T")[0] : ""}
                                  onChange={newDate => handleDueDateChange(lead.id, newDate)}
                                  isEditing={true}
                                />
                              </div>
                            </td>
                            <td className="border p-2">
                              <StatusPickerWithColor
                                type="leads"
                                currentStatus={lead.status}
                                onStatusChange={newStatus => handleStatusChange(lead.id, newStatus)}
                                className="w-full"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <p className="text-gray-600">No leads found. Try adjusting your filters.</p>
                  )}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Right Sidebar with Slack Chat */}
        <div
          className={`fixed right-0 top-200px h-[calc(100vh-200px)] transition-transform duration-300 ease-in-out ${
            isSlackDrawerOpen ? "translate-x-0" : "translate-x-[calc(100%)]"
          }`}
        >
          {/* Drawer Tab */}
          {/* <button
            onClick={() => {
              setIsSlackDrawerOpen(!isSlackDrawerOpen)
              if (!isSlackDrawerOpen) {
                setNewMessageCount(0)
              }
            }}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-full bg-white border border-gray-400 rounded-l-lg p-2 hover:bg-gray-50 transition-colors"
          >
            {isSlackDrawerOpen ? (
              <FaChevronRight size={20} color="gray" />
            ) : (
              <IoChatbubbleEllipses size={20} color="gray" className="transform -scale-x-100" />
            )}
            {!isSlackDrawerOpen && newMessageCount > 0 && (
              <span className="absolute -top-2 -left-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {newMessageCount}
              </span>
            )}
          </button> */}

          {/* Drawer Content */}
          {/* <SlackChat
            channelId="C08R74KBQTS"
            jobId={null}
            workspaceId="T0868NL444F"
            onNewMessage={() => {
              if (!isSlackDrawerOpen) {
                setNewMessageCount(prev => prev + 1)
              }
            }}
            onOpen={() => setNewMessageCount(0)}
            onDisconnect={handleSlackDisconnect}
          /> */}
        </div>
      </div>
    </ProtectedRoute>
  )
}
