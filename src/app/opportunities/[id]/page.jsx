"use client"

import { useEffect, useState, useMemo, useRef } from "react"
import { useRouter, useParams } from "next/navigation"
import { doc, getDoc, updateDoc, collection, addDoc, getDocs, deleteDoc, setDoc, onSnapshot, deleteField } from "firebase/firestore"
import { deleteObject, ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { useSelector } from "react-redux"
import { db, storage } from "@/firebase"
import AttachmentViewerModal from "@/components/modals/AttachmentViewerModal"
import UserAssignmentModal from "@/components/modals/UserAssignmentModal"
import CreateRFPModal from "@/components/modals/CreateRFPModal"
import Divider from "@/components/Divider"
import OppServicesTable from "@/components/OppServicesTable"
import { formatPhone } from "@/utils/formatPhone"
import ApprovalComponent from "@/components/ApprovalComponent"
import CreateServiceComponent from "@/components/CreateServiceComponent"
import { useApprovalLimits } from "@/context/ApprovalLimitsContext"
import { HiChevronDown, HiChevronUp, HiPencilAlt, HiCheck, HiX } from "react-icons/hi"
import { MdDoubleArrow } from "react-icons/md"
import { FaTrashAlt, FaEye } from "react-icons/fa"
import { FaPersonCirclePlus } from "react-icons/fa6"
import { useUser } from "@/hooks/useUser"
import ServicesComponent from "@/components/Services"
import { useRFPs } from "@/hooks/useRFPs"
import { useQuotes } from "@/hooks/useQuotes"
import { getNextDisplayId } from "@/utils/getNextDisplayId"
import { sortedById } from "@/utils/sortedById"
import { getOrderTypeFromId } from "@/utils/getOrderTypeFromId"
import CommentsSection from "@/components/CommentsSection"
import NotesSection from "@/components/NotesSection"
import CopyOrderButton from "@/components/CopyOrderButton"
import dayjs from "dayjs"
import ServicePicker from "@/components/ServicePicker"
import StatusPickerWithColor from "@/components/StatusPickerWithColor"
import StatusComponent from "@/components/StatusComponent"
import AddressAutocomplete from "@/components/AddressAutocomplete"
import CustomersComponent from "@/components/CustomersComponent"
import ServiceSelector from "@/components/ServiceSelector"

export default function ViewOpportunity() {
  const params = useParams()
  const id = params.id
  const [opportunity, setOpportunity] = useState(null)
  const [attachments, setAttachments] = useState([])
  const [customers, setCustomers] = useState([])
  const [isRFPModalVisible, setIsRFPModalVisible] = useState(false)
  const [services, setServices] = useState([]) // State to store services
  const [selectedAttachment, setSelectedAttachment] = useState(null)
  const [isUserAssignmentModalVisible, setIsUserAssignmentModalVisible] = useState(false)
  const [subcontractors, setSubcontractors] = useState([])
  const [globalSubcontractor, setGlobalSubcontractor] = useState(null)
  const [error, setError] = useState("")
  const router = useRouter()
  const { companyId } = useSelector(state => state.company)
  const [isRFPSectionExpanded, setIsRFPSectionExpanded] = useState(false)
  const [isQuotesSectionExpanded, setIsQuotesSectionExpanded] = useState(false)
  const [editableOpportunity, setEditableOpportunity] = useState(null)
  const [deletingAttachmentId, setDeletingAttachmentId] = useState(null)
  const [isEditing, setIsEditing] = useState(false)
  const [dragging, setDragging] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [loading, setLoading] = useState(false)
  const [newService, setNewService] = useState({
    name: "",
    description: "",
    cost: "",
    sell: "",
  })
  const { approvalLimits } = useApprovalLimits()
  const fileInputRef = useRef()
  const { admin } = useUser()
  const { rfps, deleteRFP } = useRFPs(companyId, id)
  const { quotes, deleteQuote } = useQuotes("opportunity", companyId, id)
  const [assignedUser, setAssignedUser] = useState(null)

  const fetchOpportunity = () => {
    if (!companyId || !id) return

    try {
      const docRef = doc(db, `companies/${companyId}/opportunities`, id)

      // Set up a real-time listener with onSnapshot
      const unsubscribe = onSnapshot(
        docRef,
        async docSnap => {
          if (docSnap.exists()) {
            const opportunityData = docSnap.data()
            setOpportunity(opportunityData)
            !editableOpportunity && setEditableOpportunity(opportunityData)

            // Fetch assigned user if exists
            if (opportunityData.assignedTo) {
              const userRef = doc(db, "users", opportunityData.assignedTo)
              const userSnap = await getDoc(userRef)
              if (userSnap.exists()) {
                setAssignedUser({ id: userSnap.id, ...userSnap.data() })
              }
            } else {
              setAssignedUser(null)
            }
          } else {
            setError("Opportunity not found.")
          }
        },
        err => {
          console.error("Error fetching opportunity:", err.message)
          setError("Failed to load opportunity.")
        },
      )

      // Return the unsubscribe function for cleanup
      return unsubscribe
    } catch (err) {
      console.error("Error fetching opportunity:", err.message)
      setError("Failed to load opportunity.")
    }
  }

  const fetchAttachments = () => {
    if (!companyId || !id) return

    const attachmentsRef = collection(db, `companies/${companyId}/opportunities/${id}/attachments`)

    const unsubscribe = onSnapshot(
      attachmentsRef,
      snapshot => {
        const attachmentsList = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
        setAttachments(attachmentsList)
      },
      err => {
        console.error("Error fetching attachments:", err.message)
      },
    )

    // Return the unsubscribe function to clean up the listener
    return unsubscribe
  }

  useEffect(() => {
    const fetchSubcontractors = async () => {
      try {
        const subcontractorsRef = collection(db, `companies/${companyId}/subcontractors`)
        const querySnapshot = await getDocs(subcontractorsRef)
        const fetchedSubcontractors = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))
        setSubcontractors(fetchedSubcontractors)
      } catch (err) {
        console.error("Error fetching subcontractors:", err.message)
      }
    }
    const fetchServices = async () => {
      if (!companyId || !id) return
      try {
        const servicesRef = collection(db, `companies/${companyId}/services`)
        const snapshot = await getDocs(servicesRef)
        const servicesList = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
        setServices(servicesList)
      } catch (err) {
        console.error("Error fetching services:", err.message)
      }
    }

    const unsubscribeOpportunity = fetchOpportunity()
    const unsubscribeAttachments = fetchAttachments()
    fetchServices()
    fetchSubcontractors()
    // Cleanup the listener on unmount
    return () => {
      if (unsubscribeOpportunity) unsubscribeOpportunity()
      if (unsubscribeAttachments) unsubscribeAttachments()
    }
    // fetchServices()
  }, [companyId, id])

  useEffect(() => {
    const fetchCustomers = async () => {
      if (!companyId) return
      const querySnapshot = await getDocs(collection(db, `companies/${companyId}/customers`))
      const customerList = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
      setCustomers(customerList)
    }

    // const fetchContacts = async () => {
    //   if (!companyId) return
    //   const querySnapshot = await getDocs(collection(db, `companies/${companyId}/contacts`))
    //   const contactList = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
    //   setContacts(contactList)
    // }

    fetchCustomers()
    // fetchContacts()
  }, [companyId])

  const handleEditToggle = () => {
    if (isEditing) {
      // Reset editableOpportunity to original data if editing is canceled
      setEditableOpportunity(opportunity)
    }
    setIsEditing(!isEditing)
  }

  const handleUserAssign = async user => {
    try {
      const opportunityRef = doc(db, `companies/${companyId}/opportunities`, id)
      if (user === null) {
        await updateDoc(opportunityRef, {
          assignedTo: deleteField(),
          updatedAt: new Date(),
        })
      } else {
        await updateDoc(opportunityRef, {
          assignedTo: user.id,
          updatedAt: new Date(),
        })
      }
      setIsUserAssignmentModalVisible(false)
    } catch (error) {
      console.error("Error assigning user:", error)
    }
  }

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this opportunity? This action cannot be undone.")) return

    try {
      const docRef = doc(db, `companies/${companyId}/opportunities`, id)
      await deleteDoc(docRef)
      router.push("/opportunities")
    } catch (err) {
      console.error("Error deleting opportunity:", err.message)
      setError("Failed to delete opportunity. Please try again.")
    }
  }

  const handleAddService = async () => {
    if (!newService.name || !newService.cost || !newService.sell) return

    try {
      const serviceToAdd = {
        ...newService,
        id: `${Date.now()}`,
      }

      setOpportunity(prev => ({
        ...prev,
        services: [...(prev.services || []), serviceToAdd],
      }))

      setNewService({}) // Reset form
    } catch (err) {
      console.error("Error adding service:", err.message)
      setError("Failed to add service. Please try again.")
    }
  }

  const handleViewAttachment = attachment => {
    setSelectedAttachment(attachment)
  }

  const handleRFP = () => {
    setIsRFPModalVisible(true)
  }

  const handleCloseModal = () => {
    setSelectedAttachment(null)
  }

  const handleCloseRFPModal = () => {
    setIsRFPModalVisible(false)
    fetchOpportunity()
  }

  const handleGlobalSubcontractorChange = e => {
    const selected = subcontractors.find(sub => sub.id === e.target.value)
    setGlobalSubcontractor(selected)
  }

  const handleSave = async () => {
    if (!companyId || !editableOpportunity) return
    try {
      const docRef = doc(db, `companies/${companyId}/opportunities`, id)
      await updateDoc(docRef, { ...editableOpportunity })
      setIsEditing(false) // Exit editing mode
    } catch (error) {
      console.error("Error saving opportunity details:", error.message)
    }
  }

  const handleChange = e => {
    const { name, value } = e.target

    setEditableOpportunity(prev => {
      if (name.startsWith("customer.")) {
        // If the field is part of the customer object, update it correctly
        const customerField = name.split(".")[1]
        return {
          ...prev,
          customer: {
            ...prev.customer,
            [customerField]: value,
          },
        }
      }

      // Update other form fields
      return {
        ...prev,
        [name]: value,
      }
    })

    // Handle autocomplete filtering for customer name
    if (name === "customer.name") {
      const matches = customers.filter(customer => customer.name.toLowerCase().includes(value.toLowerCase()))
      setFilteredCustomers(matches)
    }
    if (name === "siteContactName") {
      const matches = contacts.filter(contact => contact.name.toLowerCase().includes(value.toLowerCase()))
      setFilteredContacts(matches)
    }
  }

  const handleRemoveAttachment = async attachmentId => {
    try {
      // Fetch the attachment details
      const path = `companies/${companyId}/opportunities/${id}/attachments`
      const attachmentRef = doc(db, path, attachmentId)
      const attachmentDoc = await getDoc(attachmentRef)

      if (!attachmentDoc.exists()) {
        console.error("Attachment not found.")
        return
      }

      const attachmentData = attachmentDoc.data()
      const fileUrl = attachmentData.url

      // Log the file URL
      console.log("File URL:", fileUrl)

      // Extract the file path from the URL
      const storageUrlPattern = /\/o\/([^?]+)/
      const match = fileUrl.match(storageUrlPattern)

      if (!match || !match[1]) {
        throw new Error(`Failed to parse the file path from URL: ${fileUrl}`)
      }

      const filePath = decodeURIComponent(match[1])

      // Log the extracted file path
      console.log("Extracted file path:", filePath)

      // Delete the file from Firebase Storage
      const fileRef = ref(storage, filePath)
      await deleteObject(fileRef)

      // Delete the attachment record from Firestore
      await deleteDoc(attachmentRef)

      // Update local state
      // setAttachments(prev => prev.filter(att => att.id !== attachmentId))
      console.log("Attachment successfully deleted.")
    } catch (err) {
      console.error("Error removing attachment:", err.message)
    }
  }

  const handleFileUpload = async files => {
    if (!files.length) return

    setUploading(true) // Set uploading to true during the process
    try {
      const attachmentsRef = collection(db, `companies/${companyId}/opportunities/${id}/attachments`)

      const uploadedFiles = await Promise.all(
        Array.from(files).map(async file => {
          const storageRef = ref(storage, `companies/${companyId}/opportunities/${id}/attachments/${file.name}`)
          await uploadBytes(storageRef, file)
          const downloadURL = await getDownloadURL(storageRef)

          const docRef = await addDoc(attachmentsRef, {
            name: file.name,
            url: downloadURL,
          })
          return { id: docRef.id, name: file.name, url: downloadURL }
        }),
      )

      // setAttachments(prev => [...prev, ...uploadedFiles])
    } catch (err) {
      console.error("Error uploading files:", err.message)
    } finally {
      setUploading(false) // Reset uploading state after process completion
    }
  }

  const handleDragOver = e => {
    e.preventDefault()
    setDragging(true) // Set dragging state to true
  }

  const handleDragLeave = () => {
    setDragging(false) // Reset dragging state
  }

  const handleDrop = e => {
    e.preventDefault()
    setDragging(false) // Reset dragging state
    const files = e.dataTransfer.files
    handleFileUpload(files)
  }

  const handleFileSelect = e => {
    const files = e.target.files
    handleFileUpload(files)
  }

  const handleAddExistingService = async selectedService => {
    if (!selectedService) {
      console.error("No service selected")
      return
    }

    setLoading(true)
    try {
      // Check if service already exists
      const existingServiceIndex = opportunity.services?.findIndex(s => s.id === selectedService.id)
      let updatedServices = []

      if (existingServiceIndex >= 0) {
        // If service exists, increment quantity
        updatedServices = [...(opportunity.services || [])]
        updatedServices[existingServiceIndex] = {
          ...updatedServices[existingServiceIndex],
          quantity: (Number(updatedServices[existingServiceIndex].quantity) || 1) + 1,
        }
      } else {
        // If service doesn't exist, add it with quantity 1
        updatedServices = [
          ...(opportunity.services || []),
          {
            ...selectedService,
            quantity: 1,
          },
        ]
      }

      const docRef = doc(db, `companies/${companyId}/opportunities`, id)
      await updateDoc(docRef, { services: updatedServices })

      // The opportunity will automatically update through the Firestore listener
    } catch (err) {
      console.error("Error updating opportunity:", err.message)
      setError("Failed to add service. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteClick = async attachmentId => {
    if (window.confirm("Are you sure you want to delete this attachment?")) {
      setDeletingAttachmentId(attachmentId) // Start spinner
      try {
        await handleRemoveAttachment(attachmentId)
      } catch (error) {
        console.error("Error deleting attachment:", error)
      } finally {
        setDeletingAttachmentId(null) // Stop spinner
      }
    }
  }

  const handleConvertToJob = async () => {
    const confirmConvert = window.confirm("Are you sure you want to convert this opportunity to a job?")
    if (!confirmConvert) return

    try {
      // Generate the document reference to get the Firebase ID
      const newJobRef = doc(collection(db, `companies/${companyId}/jobs`))
      const newJobId = newJobRef.id // Get the generated ID

      const newJob = {
        ...editableOpportunity,
        id: newJobId, // Add the Firebase ID to the job
        displayId: await getNextDisplayId("jobs", companyId),
        status: "Setup",
        type: "Job",
        createdAt: new Date(),
      }

      // Use setDoc to set the document with the generated ID
      await setDoc(newJobRef, newJob)

      // Navigate first
      router.push(`/jobs/${newJobId}`)

      // Delete opp in the background
      Promise.resolve()
        .then(async () => {
          const opportunityDocRef = doc(db, `companies/${companyId}/opportunities`, editableOpportunity.id)
          await deleteDoc(opportunityDocRef)
        })
        .catch(error => console.error("Error deleting opportunity:", error.message))

      // alert("Opportunity successfully converted to a job.")
    } catch (error) {
      console.error("Error converting opportunity to job:", error.message)
      alert("Failed to convert opportunity to job. Please try again.")
    }
  }

  const calculateMargin = (cost, sell) => {
    if (cost === 0 || sell === 0) return "0.00%"
    return (((sell - cost) / cost) * 100).toFixed(2) + "%"
  }

  const sumQuantity = () =>
    opportunity?.services?.reduce((total, item) => {
      return total + (item.quantity || 1)
    }, 0)

  if (error) {
    return <p className="text-red-500 p-6">{error}</p>
  }

  if (!opportunity) {
    return <p className="p-6 text-gray-900">Loading...</p>
  }

  // const requiresApproval = useMemo(() => {
  //   if (!approvalLimits) return false
  //   const { opportunity: opportunityLimits } = approvalLimits
  //   const { cost, sell, total } = opportunityLimits
  //   const subtotal = calculateSubtotal(opportunity.services)
  //   return subtotal > total
  // }, [approvalLimits, opportunity])

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-auto mx-auto bg-white p-6 rounded-lg shadow-md">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">{opportunity.displayId}</h1>

          {/* Assigned User Section */}
          <div className="flex items-center gap-2">
            {assignedUser ? (
              <div className="flex items-center gap-2">
                <span className="text-gray-700">Assigned to: {assignedUser.name}</span>
                <button onClick={() => setIsUserAssignmentModalVisible(true)} className="text-blue-500 hover:text-blue-700">
                  <FaPersonCirclePlus size={22} />
                </button>
              </div>
            ) : (
              <button onClick={() => setIsUserAssignmentModalVisible(true)} className="text-blue-500 hover:text-blue-700 flex items-center gap-2">
                <FaPersonCirclePlus size={22} />
                <span>Assign User</span>
              </button>
            )}
          </div>
        </div>
        <div className="flex justify-end mb-6">
          <CopyOrderButton orderData={editableOpportunity} companyId={companyId} currentType="opportunity" />
        </div>
        <div className="mb-6">
          {/* Row 1: Order Info and Approval Status */}
          {/* Left Side: Display ID and Work Order Details */}
          <div className="flex justify-between items-center gap-4">
            {/* Display ID */}
            <div className="">
              <h1 className="text-2xl font-bold text-gray-900">{opportunity.displayId || "N/A"}</h1>
              {opportunity.originalOrderId && (
                <div className="flex gap-2 items-center">
                  <h1 className="text-sm font-light text-gray-900">Copied From:</h1>
                  <a
                    onClick={() => {
                      const orderType = getOrderTypeFromId(opportunity.originalOrderDisplayId)
                      if (orderType) {
                        router.push(`/${orderType}/${opportunity.originalOrderId}`)
                      }
                    }}
                    className="underline text-blue-600 hover:text-blue-800 cursor-pointer"
                  >
                    {opportunity.originalOrderDisplayId || "N/A"}
                  </a>
                </div>
              )}
            </div>
            {/* Work Order Details */}
            <div className="bg-gray-100 rounded-lg p-4 flex gap-6">
              <div className="flex gap-2">
                <label className="text-gray-700 font-bold">Work Order ID:</label>
                {isEditing ? (
                  <input
                    type="text"
                    name="workOrderId"
                    placeholder="Enter work order ID"
                    value={editableOpportunity?.workOrderId || ""}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  />
                ) : (
                  <p className="text-gray-900">{opportunity?.workOrderId || "N/A"}</p>
                )}
              </div>
              <div className="flex gap-2">
                <label className="text-gray-700 font-bold">Created Date:</label>
                <p className="text-gray-900">{dayjs(opportunity?.createdAt.toDate()).format("M/D/YYYY") || "N/A"}</p>
              </div>
              <div className="flex gap-2">
                <label className="text-gray-700 font-bold">Term:</label>
                <p>{customers.find(c => c.id === opportunity.customer.id)?.term || "N/A"}</p>
              </div>
            </div>
            {opportunity.approvalStatus ? (
              <span className="px-4 py-2 rounded-full text-white font-bold bg-yellow-500">Pending Approval</span>
            ) : (
              <span
                className={`px-4 py-2 rounded-full text-white font-bold ${
                  opportunity.status === "New" ? "bg-green-500" : opportunity.status === "Open" ? "bg-blue-500" : "bg-gray-500"
                }`}
              >
                {opportunity.status}
              </span>
            )}
          </div>
          {/* Right Side: Approval Status */}

          {/* Row 2: Assigned User */}
          <div className="mt-4 flex justify-center">
            <p className="text-gray-900">
              <strong>Assigned User:</strong> {opportunity.assignedUser?.name || opportunity.assignedUser?.email || "N/A"}
            </p>
          </div>
        </div>

        {/* {!isEditing ? (
          <button onClick={handleEditToggle} className="text-blue-500 hover:text-blue-700">
            <HiPencilAlt size={24} />
          </button>
        ) : (
          <div className="absolute right-0 top-0">
            <button onClick={handleSave} className="text-green-500 hover:text-green-700">
              <HiCheck size={24} />
            </button>
            <button onClick={handleEditToggle} className="text-red-500 hover:text-red-700 ml-4">
              <HiX size={24} />
            </button>
          </div>
        )} */}
        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Work Order ID:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="workOrderId"
                  placeholder="Enter work order ID"
                  value={editableOpportunity?.workOrderId || ""}
                  onChange={handleChange}
                  // className="border border-gray-300 rounded p-2 text-gray-900 w-full"
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{opportunity?.workOrderId || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-2 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Job Name:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="name"
                  placeholder="Enter job name"
                  value={editableOpportunity?.name || ""}
                  onChange={handleChange}
                  // className="border border-gray-300 rounded p-2 text-gray-900 w-full"
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{opportunity?.name || "N/A"}</p>
              )}
            </div>
            {!isEditing ? (
              <button onClick={handleEditToggle} className="text-blue-500 hover:text-blue-700 absolute right-0 top-0">
                <HiPencilAlt size={24} />
              </button>
            ) : (
              <div className="absolute right-0 top-0">
                <button onClick={handleSave} className="text-green-500 hover:text-green-700">
                  <HiCheck size={24} />
                </button>
                <button onClick={handleEditToggle} className="text-red-500 hover:text-red-700 ml-4">
                  <HiX size={24} />
                </button>
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Customer:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="customer.name"
                  placeholder="Enter customer name"
                  value={editableOpportunity?.customer.name || ""}
                  onChange={handleChange}
                  // className="border border-gray-300 rounded p-2 text-gray-900 w-full"
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{opportunity?.customer.name || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Location:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="location"
                  placeholder="Enter job location"
                  value={editableOpportunity?.location || ""}
                  onChange={handleChange}
                  // className="border border-gray-300 rounded p-2 text-gray-900 w-full"
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{opportunity?.location || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Contact Name:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="siteContactName"
                  placeholder="Enter contact name"
                  value={editableOpportunity?.siteContactName || ""}
                  onChange={handleChange}
                  // className="border border-gray-300 rounded p-2 text-gray-900 w-full"
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{opportunity?.siteContactName || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Contact Email:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="siteContactEmail"
                  placeholder="Enter contact email"
                  value={editableOpportunity?.siteContactEmail || ""}
                  onChange={handleChange}
                  // className="border border-gray-300 rounded p-2 text-gray-900 w-full"
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{opportunity?.siteContactEmail || "N/A"}</p>
              )}
            </div>
            <div className={`col-span-1 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Contact Phone:</label>
              {isEditing ? (
                <input
                  type="text"
                  name="siteContactPhone"
                  placeholder="Enter contact phone"
                  value={editableOpportunity?.siteContactPhone || ""}
                  onChange={handleChange}
                  // className="border border-gray-300 rounded p-2 text-gray-900 w-full"
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                />
              ) : (
                <p className="text-gray-900">{formatPhone(opportunity?.siteContactPhone) || "N/A"}</p>
              )}
            </div>
            {/* <div className={`col-span-3 gap-2 ${!isEditing && "flex"}`}>
              <label className="block text-gray-700 font-bold">Notes:</label>
              {isEditing ? (
                <textarea
                  type="text"
                  name="notes"
                  value={editableOpportunity?.notes || ""}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                  rows={4}
                />
              ) : (
                <p className="text-gray-900">{opportunity?.notes || "N/A"}</p>
              )}
            </div> */}
            <div className="w-full col-span-3">
              <NotesSection orderId={opportunity.id} orderType="opportunity" />
            </div>
            <div className="mb-4">
              <label className="block text-gray-900 font-bold mb-2">Assign Subcontractor to All</label>
              <select
                value={globalSubcontractor?.id || ""}
                onChange={handleGlobalSubcontractorChange}
                className="w-full border border-gray-300 rounded p-2 text-gray-900"
              >
                <option value="">Select Subcontractor</option>
                {subcontractors.map(sub => (
                  <option key={sub.id} value={sub.id}>
                    {sub.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        <Divider />
        {/* Add Service Section */}
        <div className="mt-6">
          <OppServicesTable
            opportunity={opportunity}
            subcontractors={subcontractors}
            globalSubcontractor={globalSubcontractor}
            onCreateInvoice={() => console.log("CREATE INVOICE")}
          />
          <ServicePicker order={opportunity} onAddService={handleAddExistingService} />
          <Divider />
          <CommentsSection orderId={opportunity.id} orderType="opportunity" />
          <Divider />
          <CreateServiceComponent newService={newService} setNewService={setNewService} handleAddService={handleAddExistingService} />
        </div>
        <Divider />
        {/* RFPs Section */}
        <div className="mt-6">
          <div className="flex items-center mb-2">
            <div className="cursor-pointer" onClick={() => setIsRFPSectionExpanded(prev => !prev)}>
              {isRFPSectionExpanded ? (
                <HiChevronUp className="text-gray-900 w-6 h-6 mr-2" />
              ) : (
                <HiChevronDown className="text-gray-900 w-6 h-6 mr-2" />
              )}
            </div>
            <h2 className="text-xl font-bold text-gray-900">RFPs {rfps?.length ? `(${rfps.length})` : ""}</h2>
          </div>

          {isRFPSectionExpanded && (
            <div className="overflow-x-auto">
              {rfps && rfps.length > 0 ? (
                <table className="table-auto w-full border-collapse border border-gray-300 text-left">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border p-4 text-gray-900 w-1/6">Display ID</th>
                      <th className="border p-4 text-gray-900 w-1/6">Timestamp</th>
                      <th className="border p-4 text-gray-900 w-fit">Services</th>
                      <th className="border p-4 text-gray-900 w-1/5">Subcontractor</th>
                      <th className="border p-4 text-gray-900 w-1">Subtotal</th>
                      <th className="border p-4 text-gray-900 w-1">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedById(rfps)?.map(rfp => (
                      <tr key={rfp.id} className="hover:bg-gray-50">
                        <td className="border p-2 text-blue-500 underline cursor-pointer">
                          <a key={rfp.id} href={rfp.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                            {rfp.displayId}
                          </a>
                        </td>
                        <td className="border p-2 text-gray-800">{dayjs(rfp.createdAt.toDate()).format("MMM D, YYYY h:mma")}</td>
                        <td className="border p-2 text-gray-800 break-words">
                          {rfp.services?.length > 0 ? rfp.services.map(service => service.name).join(", ") : "No services listed"}
                        </td>
                        <td className="border p-2 text-gray-800">{rfp.subcontractor?.name || "Unassigned"}</td>
                        <td className="border p-2 text-gray-800">
                          ${rfp.subtotal?.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || "0.00"}
                        </td>
                        <td className="p-4 border border-gray-300">
                          <div className="flex justify-evenly gap-2">
                            <button onClick={e => deleteRFP(rfp.id)} className="text-red-500 hover:text-red-700">
                              <FaTrashAlt size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-gray-500">No RFPs available.</p>
              )}
            </div>
          )}
        </div>
        <Divider />
        {/* Quotes Section */}
        <div className="mt-6">
          <div className="flex items-center mb-2">
            <div className="cursor-pointer" onClick={() => setIsQuotesSectionExpanded(prev => !prev)}>
              {isQuotesSectionExpanded ? (
                <HiChevronUp className="text-gray-900 w-6 h-6 mr-2" />
              ) : (
                <HiChevronDown className="text-gray-900 w-6 h-6 mr-2" />
              )}
            </div>
            <h2 className="text-xl font-bold text-gray-900">Quotes {quotes?.length ? `(${quotes.length})` : ""}</h2>
          </div>
          {isQuotesSectionExpanded && (
            <div className="overflow-x-auto">
              {quotes && quotes.length > 0 ? (
                <table className="table-auto w-full border-collapse border border-gray-300 text-left">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="border p-4 text-gray-900 w-1/6">Display ID</th>
                      <th className="border p-4 text-gray-900 w-1/6">Timestamp</th>
                      <th className="border p-4 text-gray-900 w-fit">Services</th>
                      {/* <th className="border p-4 text-gray-900 w-24">Sell</th> */}
                      <th className="border p-4 text-gray-900 w-1">Subtotal</th>
                      <th className="border p-4 text-gray-900 w-1">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sortedById(quotes)?.map(quote => (
                      <tr key={quote.id} className="hover:bg-gray-50">
                        <td className="border p-2 text-blue-500 underline cursor-pointer">
                          <a key={quote.id} href={quote.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                            {quote.displayId}
                          </a>
                        </td>
                        <td className="border p-2 text-gray-800">{dayjs(quote.createdAt.toDate()).format("MMM D, YYYY h:mma")}</td>
                        <td className="border p-2 text-gray-800">{quote.services.map(q => q.name).join(", ")}</td>
                        {/* <td className="border p-2 text-gray-800"> */}
                        {/* ${quote.sell?.toFixed(2)} */}
                        {/* {quote.items?.map(item => (
                          <div key={item.id}>
                            {item.name}: ${item.sell.toFixed(2)}
                          </div>
                        ))} */}
                        {/* </td> */}
                        <td className="border p-2 text-gray-800">
                          ${quote.subtotal?.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                        </td>
                        <td className="p-4 border border-gray-300">
                          <div className="flex justify-evenly gap-2">
                            <button onClick={e => deleteQuote(quote.id)} className="text-red-500 hover:text-red-700">
                              <FaTrashAlt size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-gray-500">No quotes available.</p>
              )}
            </div>
          )}
        </div>
        <Divider />
        {/* Attachments Section */}
        <div className="col-span-3">
          <h2 className="text-lg font-bold mb-4 text-gray-900">Attachments</h2>
          {/* Hidden file input */}
          <input ref={fileInputRef} multiple type="file" style={{ display: "none" }} onChange={handleFileSelect} />
          {/* Drop Zone */}
          <div
            className={`relative border-2 flex justify-center items-center text-center rounded mb-6 cursor-pointer text-gray-500 h-48 ${
              dragging ? "border-blue-500 bg-blue-100" : "border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current.click()}
          >
            <span>{dragging ? "Release to upload" : "Drag & Drop files here or click to upload"}</span>
            {uploading && (
              <div className="absolute inset-0 flex justify-center items-center bg-white bg-opacity-50 z-10">
                <div className="loader border-t-4 border-blue-500 w-8 h-8 rounded-full animate-spin"></div>
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {(attachments || []).map(attachment => (
              <div key={attachment.id} className="border p-2 rounded shadow-md flex items-center gap-4">
                {/* Image Section */}
                <img src={attachment.url} alt={attachment.name} className="w-16 h-16 object-cover rounded" />
                <div className="flex-1">
                  <a href={attachment.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                    {attachment.name}
                  </a>
                </div>
                <div className="flex gap-4 pr-1">
                  <button onClick={e => handleOpenModal(e, attachment)} className="text-blue-500 hover:text-blue-700 p-1" title="View Attachment">
                    <FaEye size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDeleteClick(attachment.id)}
                    className={`text-red-500 hover:text-red-700 p-1 ${deletingAttachmentId === attachment.id ? "cursor-wait" : ""}`}
                    title="Delete Attachment"
                    disabled={deletingAttachmentId === attachment.id}
                  >
                    {deletingAttachmentId === attachment.id ? (
                      <div className="absolute inset-0 flex justify-center items-center">
                        <div className="loader border-t-4 border-red-500 w-4 h-4 rounded-full animate-spin"></div>
                      </div>
                    ) : (
                      <FaTrashAlt size={16} />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="mt-6 flex justify-end gap-4">
          <div className="gap-4 flex">
            <button
              className={`block w-full text-left rounded p-2 text-white bg-green-400 hover:bg-green-300 ${
                opportunity?.status === "Closed" && "text-gray-400 cursor-not-allowed"
              }`}
              onClick={handleConvertToJob}
            >
              <div className="flex justify-center items-center text-white">
                <MdDoubleArrow className={`inline mr-2 ${opportunity?.status === "Closed" ? "text-gray-400 cursor-not-allowed" : "text-white"}`} />
                Convert to Job
              </div>
            </button>
            {admin && (
              <button className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded" onClick={handleDelete}>
                Delete Opportunity
              </button>
            )}
          </div>
          {/* <button className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600" onClick={() => router.push("/opportunities")}>
            Back to Opportunities
          </button> */}
        </div>
      </div>
      {/* RFP Modal */}
      {isRFPModalVisible && (
        <CreateRFPModal isOpen={isRFPModalVisible} opportunity={opportunity} onClose={handleCloseRFPModal} companyId={companyId} />
      )}
      {/* Attachment Viewer Modal */}
      {selectedAttachment && <AttachmentViewerModal isOpen={!!selectedAttachment} attachment={selectedAttachment} onClose={handleCloseModal} />}
      {isUserAssignmentModalVisible && (
        <UserAssignmentModal
          onClose={() => setIsUserAssignmentModalVisible(false)}
          onAssign={handleUserAssign}
          defaultUserId={opportunity?.assignedTo}
          type="opportunity"
          entityId={opportunity.id}
          entityName={opportunity.name || opportunity.id}
          entityDisplayId={opportunity.displayId}
          assignedTo={opportunity?.assignedTo}
        />
      )}
    </div>
  )
}
