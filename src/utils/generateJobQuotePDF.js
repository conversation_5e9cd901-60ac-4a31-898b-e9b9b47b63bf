import jsPDF from "jspdf"
import autoTable from "jspdf-autotable"

export async function generateJobQuotePDF({ companyLogoURL, companyName, quoteId, jobDetails, lineItems, subtotal, notes, selectedTemplateContent }) {
  const doc = new jsPDF()

  // Helper function to format currency
  const formatCurrency = amount =>
    `$${parseFloat(amount || 0).toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`

  const pageWidth = doc.internal.pageSize.width
  const pageHeight = doc.internal.pageSize.height
  const margin = 15
  const rowHeight = 8
  const headerHeight = 12
  const titleHeight = 15
  const bufferSpace = 10

  // Add company name to top-left
  doc.setFontSize(24)
  doc.text(companyName || "Your Company", margin, margin + 10)

  // Add "Job Quote" text
  doc.setFontSize(16)
  doc.setTextColor(0, 0, 0)
  doc.text("Quote ID: " + quoteId, margin, margin + 20)
  doc.setTextColor(0, 0, 0)

  // Add company logo to top-right if available
  if (companyLogoURL) {
    try {
      const img = new window.Image()
      img.src = companyLogoURL
      await new Promise(resolve => {
        img.onload = resolve
      })
      const containerWidth = 60
      const containerHeight = 30
      let logoWidth = containerWidth
      let logoHeight = containerHeight
      const aspectRatio = img.width / img.height
      if (aspectRatio > 1) {
        logoHeight = containerWidth / aspectRatio
      } else {
        logoWidth = containerHeight * aspectRatio
      }
      const logoX = pageWidth - margin - containerWidth
      const logoY = margin + 15 - logoHeight / 2
      doc.addImage(img, "PNG", logoX, logoY, logoWidth, logoHeight)
    } catch (error) {
      console.warn("Failed to load company logo:", error)
    }
  }

  // Company and Project Information
  doc.setFontSize(12)
  let currentY = margin + 35
  // if (quoteId) {
  //   doc.text(`Quote ID: ${quoteId}`, margin, currentY)
  //   currentY += 6
  // }
  doc.text(`Date: ${new Date().toLocaleDateString()}`, margin, currentY)
  currentY += 6
  if (jobDetails?.projectName) {
    doc.text(`Job Name: ${jobDetails.projectName}`, margin, currentY)
    currentY += 6
  }
  if (jobDetails?.customerName) {
    doc.text(`Client: ${jobDetails.customerName}`, margin, currentY)
    currentY += 6
  }
  if (jobDetails?.projectId) {
    doc.text(`Project ID: ${jobDetails.projectId}`, margin, currentY)
    currentY += 6
  }
  if (jobDetails?.location) {
    doc.text(`Location: ${jobDetails.location}`, margin, currentY)
    currentY += 6
  }
  // if (jobDetails?.customer?.name) {
  //   doc.text(`Client: ${jobDetails.customer.name}`, margin, currentY)
  //   currentY += 10
  // }

  // Add a horizontal line
  // doc.setDrawColor(200, 200, 200)
  // doc.line(margin, currentY, pageWidth - margin, currentY)
  currentY += 4

  // Table headers
  const tableHeaders = [["Name", "Quantity", "Price", "Total"]]

  // Format line items with description rows
  const tableBody = []
  ;(lineItems || []).forEach(item => {
    tableBody.push([
      item.name || "",
      (item.quantity || 1).toString(),
      formatCurrency(item.sell),
      formatCurrency((item.sell || 0) * (item.quantity || 1)),
    ])
    if (item.description) {
      tableBody.push([
        { content: "        Description:", styles: { textColor: [100, 100, 100], fontSize: 9, cellPadding: 2 } },
        { content: item.description, colSpan: 3, styles: { textColor: [100, 100, 100], fontSize: 9, cellPadding: 2 } },
        "",
        "",
      ])
    }
  })

  // Table
  autoTable(doc, {
    startY: currentY,
    head: tableHeaders,
    body: tableBody,
    theme: "grid",
    styles: { fontSize: 10 },
    columnStyles: {
      0: { cellWidth: "auto" },
      1: { cellWidth: 30, halign: "center" },
      2: { cellWidth: 40, halign: "right" },
      3: { cellWidth: 40, halign: "right" },
    },
    didParseCell: function (data) {
      // Make description row background lighter
      if (data.row.raw && data.row.raw[0] && data.row.raw[0].content === "Description:") {
        data.cell.styles.fillColor = [245, 245, 245]
      }
    },
  })

  // Subtotal row
  autoTable(doc, {
    startY: doc.lastAutoTable.finalY + 2,
    body: [["Subtotal", "", "", formatCurrency(subtotal)]],
    theme: "plain",
    styles: { fontSize: 10, fontStyle: "bold" },
    columnStyles: {
      3: { halign: "right" },
    },
  })

  let yAfterTable = doc.lastAutoTable.finalY + 15

  // Notes Section
  if (notes && notes.length > 0) {
    doc.setFontSize(12)
    doc.text("Notes:", margin, yAfterTable)
    let currentNoteY = yAfterTable + 8
    doc.setFontSize(10)
    notes.forEach(note => {
      if (!note) return
      const noteText = typeof note === "string" ? note : note.text
      if (!noteText) return
      const splitNote = doc.splitTextToSize(noteText, pageWidth - 2 * margin)
      splitNote.forEach(line => {
        if (currentNoteY > pageHeight - margin - 20) {
          doc.addPage()
          currentNoteY = margin + 20
        }
        doc.text(line, margin, currentNoteY)
        currentNoteY += 7
      })
      currentNoteY += 5
    })
    yAfterTable = currentNoteY + 10
  }

  // Terms/Template Content (second page)
  // if (selectedTemplateContent) {
  //   doc.addPage()
  //   const width = doc.internal.pageSize.width
  //   const height = doc.internal.pageSize.height
  //   const maxWidth = width - 2 * margin
  //   const fontSize = 10
  //   let yTemplate = margin + 20
  //   doc.setFontSize(16)
  //   doc.setFont("helvetica", "bold")
  //   doc.text("Terms and Conditions", margin, yTemplate)
  //   yTemplate += 15
  //   doc.setDrawColor(200, 200, 200)
  //   doc.line(margin, yTemplate, width - margin, yTemplate)
  //   yTemplate += 15
  //   doc.setFontSize(fontSize)
  //   doc.setFont("helvetica", "normal")
  //   const standardizedContent = selectedTemplateContent
  //     .replace(/\r\n/g, "\n")
  //     .replace(/\n{3,}/g, "\n\n")
  //     .trim()
  //   let paragraphs = standardizedContent
  //     .split(/\n\n/)
  //     .map(paragraph => paragraph.trim())
  //     .filter(paragraph => paragraph.length > 0)
  //   paragraphs.forEach((paragraph, index) => {
  //     if (yTemplate > height - margin - 20) {
  //       doc.addPage()
  //       yTemplate = margin + 20
  //     }
  //     const wrappedLines = doc.splitTextToSize(paragraph, maxWidth)
  //     wrappedLines.forEach(line => {
  //       if (yTemplate > height - margin - 10) {
  //         doc.addPage()
  //         yTemplate = margin + 20
  //       }
  //       doc.text(line, margin, yTemplate)
  //       yTemplate += 7
  //     })
  //     yTemplate += 5
  //     if (index < paragraphs.length - 1) {
  //       if (yTemplate > height - margin - 15) {
  //         doc.addPage()
  //         yTemplate = margin + 20
  //       }
  //       doc.setDrawColor(200, 200, 200)
  //       doc.line(margin, yTemplate, width - margin, yTemplate)
  //       yTemplate += 15
  //     }
  //   })
  // }

  // Return the PDF as a blob
  const pdfBlob = doc.output("blob")
  return new Blob([pdfBlob], { type: "application/pdf" })
}
