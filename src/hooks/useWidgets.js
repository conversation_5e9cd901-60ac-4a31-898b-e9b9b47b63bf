"use client"

import { useState, useEffect } from "react"
import { doc, getDoc, setDoc } from "firebase/firestore"
import { useSelector } from "react-redux"
import { db } from "@/firebase"

// Default widget configuration
const DEFAULT_WIDGETS = [
  {
    id: "accounting-summary",
    type: "AccountingSummary",
    title: "Accounting Summary",
    enabled: true,
    position: 0,
    size: "full",
    config: {}
  },
  {
    id: "completed-jobs",
    type: "CompletedJobs",
    title: "Completed and Paid Jobs",
    enabled: true,
    position: 1,
    size: "full",
    config: {}
  },
  {
    id: "invoiced-jobs",
    type: "InvoicedJobs",
    title: "Invoiced Jobs Summary",
    enabled: true,
    position: 2,
    size: "full",
    config: {},
    adminOnly: true
  },
  {
    id: "job-status-chart",
    type: "JobStatusChart",
    title: "Job Status Overview",
    enabled: true,
    position: 3,
    size: "half",
    config: {}
  },
  {
    id: "opportunity-status-chart",
    type: "OpportunityStatusChart",
    title: "Opportunity Status Overview",
    enabled: false,
    position: 4,
    size: "half",
    config: {}
  },
  {
    id: "lead-status-chart",
    type: "LeadStatusChart",
    title: "Lead Status Overview",
    enabled: false,
    position: 5,
    size: "half",
    config: {}
  },
  {
    id: "jobs-table",
    type: "JobsTable",
    title: "Jobs Table",
    enabled: true,
    position: 6,
    size: "full",
    config: {}
  },
  {
    id: "opportunities-table",
    type: "OpportunitiesTable",
    title: "Opportunities Table",
    enabled: false,
    position: 7,
    size: "full",
    config: {}
  },
  {
    id: "leads-table",
    type: "LeadsTable",
    title: "Leads Table",
    enabled: false,
    position: 8,
    size: "full",
    config: {}
  }
]

export function useWidgets() {
  const [widgets, setWidgets] = useState(DEFAULT_WIDGETS)
  const [loading, setLoading] = useState(true)
  const { companyId } = useSelector(state => state.company)
  const { user } = useSelector(state => state.auth)

  // Load user's widget preferences
  useEffect(() => {
    loadWidgetPreferences()
  }, [companyId, user?.id])

  const loadWidgetPreferences = async () => {
    if (!companyId || !user?.id) return

    try {
      const prefsRef = doc(db, `companies/${companyId}/users/${user.id}/preferences`, "widgets")
      const prefsDoc = await getDoc(prefsRef)

      if (prefsDoc.exists()) {
        const savedWidgets = prefsDoc.data().widgets || DEFAULT_WIDGETS
        setWidgets(savedWidgets)
      } else {
        setWidgets(DEFAULT_WIDGETS)
      }
    } catch (error) {
      console.error("Error loading widget preferences:", error)
      setWidgets(DEFAULT_WIDGETS)
    } finally {
      setLoading(false)
    }
  }

  const saveWidgetPreferences = async (newWidgets) => {
    if (!companyId || !user?.id) return

    try {
      const prefsRef = doc(db, `companies/${companyId}/users/${user.id}/preferences`, "widgets")
      await setDoc(prefsRef, { widgets: newWidgets }, { merge: true })
    } catch (error) {
      console.error("Error saving widget preferences:", error)
    }
  }

  const updateWidget = (widgetId, updates) => {
    const newWidgets = widgets.map(widget =>
      widget.id === widgetId ? { ...widget, ...updates } : widget
    )
    setWidgets(newWidgets)
    saveWidgetPreferences(newWidgets)
  }

  const toggleWidget = (widgetId) => {
    const widget = widgets.find(w => w.id === widgetId)
    if (widget) {
      updateWidget(widgetId, { enabled: !widget.enabled })
    }
  }

  const addWidget = (widgetConfig) => {
    const newWidget = {
      ...widgetConfig,
      id: `${widgetConfig.type}-${Date.now()}`,
      position: widgets.length,
      enabled: true
    }
    const newWidgets = [...widgets, newWidget]
    setWidgets(newWidgets)
    saveWidgetPreferences(newWidgets)
  }

  const removeWidget = (widgetId) => {
    const newWidgets = widgets.filter(widget => widget.id !== widgetId)
    setWidgets(newWidgets)
    saveWidgetPreferences(newWidgets)
  }

  const reorderWidgets = (newOrder) => {
    const reorderedWidgets = newOrder.map((widget, index) => ({
      ...widget,
      position: index
    }))
    setWidgets(reorderedWidgets)
    saveWidgetPreferences(reorderedWidgets)
  }

  // Get enabled widgets sorted by position
  const enabledWidgets = widgets
    .filter(widget => widget.enabled)
    .sort((a, b) => a.position - b.position)

  // Get available widget types for adding
  const availableWidgetTypes = [
    { type: "AccountingSummary", title: "Accounting Summary", description: "Financial metrics and time range controls" },
    { type: "CompletedJobs", title: "Completed Jobs", description: "Table of completed and paid jobs" },
    { type: "InvoicedJobs", title: "Invoiced Jobs", description: "Summary of invoiced jobs (Admin only)", adminOnly: true },
    { type: "JobStatusChart", title: "Job Status Chart", description: "Bar chart showing job status distribution" },
    { type: "OpportunityStatusChart", title: "Opportunity Status Chart", description: "Bar chart for opportunities" },
    { type: "LeadStatusChart", title: "Lead Status Chart", description: "Bar chart for leads" },
    { type: "JobsTable", title: "Jobs Table", description: "Filterable table of jobs" },
    { type: "OpportunitiesTable", title: "Opportunities Table", description: "Filterable table of opportunities" },
    { type: "LeadsTable", title: "Leads Table", description: "Filterable table of leads" }
  ]

  return {
    widgets,
    enabledWidgets,
    availableWidgetTypes,
    loading,
    updateWidget,
    toggleWidget,
    addWidget,
    removeWidget,
    reorderWidgets
  }
}
