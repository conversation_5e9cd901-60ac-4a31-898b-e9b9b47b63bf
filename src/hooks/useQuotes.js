import { useState, useEffect } from "react"
import { collection, doc, deleteDoc, onSnapshot, getDoc } from "firebase/firestore"
import { getStorage, ref, deleteObject } from "firebase/storage"
import { db } from "@/firebase"

export const useQuotes = (type, companyId, orderId) => {
  const [quotes, setQuotes] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  function getOrderType() {
    let collectionPath = ""

    switch (type) {
      case "opportunity":
        collectionPath = "opportunities"
        break
      case "job":
        collectionPath = "jobs"
        break
      case "workOrder":
        collectionPath = "workOrders"
        break
      default:
        throw new Error("Invalid orderType")
    }

    return collectionPath
  }

  useEffect(() => {
    if (!companyId || !orderId) {
      setLoading(false)
      return
    }

    const quoteCollectionRef = collection(db, `companies/${companyId}/${getOrderType()}/${orderId}/quotes`)

    const unsubscribe = onSnapshot(
      quoteCollectionRef,
      snapshot => {
        const fetchedQuotes = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))
        setQuotes(fetchedQuotes)
        setLoading(false)
      },
      err => {
        console.error("Error fetching Quotes:", err)
        setError(err.message)
        setLoading(false)
      },
    )

    return () => unsubscribe()
  }, [companyId, orderId])

  /** 🔥 Delete a Quote */
  const deleteQuote = async quoteId => {
    if (!companyId || !orderId || !quoteId) {
      console.error("Missing parameters for deleting Quote")
      return { success: false, error: "Invalid Quote data" }
    }

    try {
      const quoteRef = doc(db, `companies/${companyId}/${getOrderType()}/${orderId}/quotes/${quoteId}`)
      const quoteSnap = await getDoc(quoteRef)

      if (!quoteSnap.exists()) {
        console.error("Quote not found")
        return { success: false, error: "Quote not found" }
      }

      const quoteData = quoteSnap.data()
      const pdfUrl = quoteData.pdfUrl

      // Delete the Quote document from Firestore
      await deleteDoc(quoteRef)

      // Delete the associated PDF file from Firebase Storage (if exists)
      if (pdfUrl) {
        const storage = getStorage()
        const pdfRef = ref(storage, pdfUrl)

        try {
          await deleteObject(pdfRef)
          console.log("Quote PDF deleted successfully")
        } catch (storageError) {
          console.warn("Failed to delete Quote PDF:", storageError.message)
        }
      }

      // Optimistically update state
      setQuotes(prev => prev.filter(quote => quote.id !== quoteId))

      console.log("Quote successfully deleted")
      return { success: true }
    } catch (error) {
      console.error("Error deleting Quote:", error.message)
      return { success: false, error: error.message }
    }
  }

  return { quotes, loading, error, deleteQuote }
}
