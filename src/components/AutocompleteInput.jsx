import { useState, useEffect, useRef } from "react"
import { toTitleCase } from "@/utils/toTitleCase"

export default function AutocompleteInput({ value, onChange, onSelect, suggestions, className = "" }) {
  const [inputValue, setInputValue] = useState(value)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredSuggestions, setFilteredSuggestions] = useState([])
  const wrapperRef = useRef(null)

  useEffect(() => {
    setInputValue(value)
  }, [value])

  useEffect(() => {
    const handleClickOutside = event => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setShowSuggestions(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const handleInputChange = e => {
    const newValue = e.target.value
    setInputValue(newValue)
    onChange(newValue)

    // Filter suggestions
    if (newValue.trim() === "") {
      setFilteredSuggestions([])
      setShowSuggestions(false)
      return
    }

    const filtered = suggestions.filter(suggestion => suggestion.name.toLowerCase().includes(newValue.toLowerCase()))
    setFilteredSuggestions(filtered)
    setShowSuggestions(true)
  }

  const handleSuggestionClick = suggestion => {
    setInputValue(suggestion.name)
    onChange(suggestion.name)
    onSelect(suggestion)
    setShowSuggestions(false)
  }

  return (
    <div ref={wrapperRef} className="relative w-full">
      <input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onFocus={() => inputValue.trim() !== "" && setShowSuggestions(true)}
        className={className}
      />
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={suggestion.id || index}
              className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex justify-between items-center"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <span>{suggestion.name}</span>
              {suggestion.sell && (
                <span className="text-gray-500 text-sm">
                  ${suggestion.sell.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
