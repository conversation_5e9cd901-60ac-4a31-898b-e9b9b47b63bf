import { useState, useRef, useEffect } from "react"
import { doc, updateDoc } from "firebase/firestore"
import { db } from "@/firebase"
import { useSelector } from "react-redux"
import dayjs from "dayjs"
import { FaPaperPlane, FaStickyNote } from "react-icons/fa"

const NotesPopup = ({ notes, isOpen, jobId, companyId, onMouseLeave }) => {
  const [newNote, setNewNote] = useState("")
  const [loading, setLoading] = useState(false)
  const { user } = useSelector(state => state.auth)
  const popupRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = event => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onMouseLeave()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onMouseLeave])

  const handleAddNote = async () => {
    if (!newNote.trim()) return
    if (!user) {
      alert("You must be logged in to add a note.")
      return
    }

    setLoading(true)
    const updatedNotes = [
      ...(Array.isArray(notes) ? notes : []),
      {
        author: {
          uid: user.id || "",
          name: user.name || "",
          email: user.email || "",
        },
        text: newNote.trim(),
        timestamp: new Date().toISOString(),
      },
    ]

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, jobId)
      await updateDoc(docRef, { notes: updatedNotes })
      setNewNote("")
    } catch (err) {
      console.error("Error adding note:", err.message)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div ref={popupRef} className="absolute right-0 top-4 w-80 bg-white border border-gray-300 rounded shadow-lg z-10">
      <div className="p-4 border-b">
        <h3 className="font-bold text-gray-900 mb-2 flex items-center">
          <FaStickyNote className="mr-2" /> Notes {notes && notes.length > 0 && <span className="text-sm text-gray-700 ml-1">({notes.length})</span>}
        </h3>

        {/* Add Note Section */}
        <div className="flex items-center">
          <textarea
            type="text"
            placeholder="Add a note..."
            value={newNote}
            onChange={e => setNewNote(e.target.value)}
            className="w-full border border-gray-300 rounded p-2 text-gray-900"
            rows={2}
          />
          <button
            onClick={handleAddNote}
            disabled={loading || !newNote.trim()}
            className={`ml-2 p-2 rounded ${loading ? "opacity-50 cursor-not-allowed" : "hover:bg-blue-600"} ${
              newNote.trim() ? "bg-blue-500 text-white hover:bg-blue-600" : "bg-gray-300 text-gray-500 cursor-not-allowed"
            }`}
          >
            <FaPaperPlane />
          </button>
        </div>
      </div>

      {/* Notes List */}
      <div className="p-4 max-h-96 overflow-y-auto">
        {notes && notes.length > 0 ? (
          <div className="space-y-2">
            {notes.map((note, index) => (
              <div key={index} className="p-2 bg-gray-50 rounded">
                <p className="font-bold text-gray-900">
                  {note.author?.name || note.author?.email} <span className="text-gray-500 text-sm">• {dayjs(note.timestamp).format("M/D/YY")}</span>
                </p>
                <p className="text-gray-700">{note.text}</p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 italic">No notes yet.</p>
        )}
      </div>
    </div>
  )
}

export default NotesPopup
