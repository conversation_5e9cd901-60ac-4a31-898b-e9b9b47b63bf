"use client"

import { useState } from "react"
import { FaPlus, FaTimes, FaEye, FaEyeSlash } from "react-icons/fa"
import { useUser } from "@/hooks/useUser"

export default function WidgetMenu({ 
  widgets, 
  availableWidgetTypes, 
  onAddWidget, 
  onToggleWidget,
  isOpen,
  onClose 
}) {
  const [selectedType, setSelectedType] = useState("")
  const { admin } = useUser()

  const handleAddWidget = () => {
    if (!selectedType) return

    const widgetType = availableWidgetTypes.find(type => type.type === selectedType)
    if (!widgetType) return

    const newWidget = {
      type: widgetType.type,
      title: widgetType.title,
      size: "full",
      config: {}
    }

    onAddWidget(newWidget)
    setSelectedType("")
  }

  const getEnabledWidgetsByType = (type) => {
    return widgets.filter(widget => widget.type === type && widget.enabled).length
  }

  const getDisabledWidgetsByType = (type) => {
    return widgets.filter(widget => widget.type === type && !widget.enabled)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-bold text-gray-900">Manage Dashboard Widgets</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <FaTimes size={20} />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Add New Widget Section */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Widget</h3>
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Widget Type
                </label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                >
                  <option value="">Select a widget type...</option>
                  {availableWidgetTypes
                    .filter(type => !type.adminOnly || admin)
                    .map(type => (
                      <option key={type.type} value={type.type}>
                        {type.title}
                      </option>
                    ))
                  }
                </select>
                {selectedType && (
                  <p className="text-sm text-gray-600 mt-1">
                    {availableWidgetTypes.find(type => type.type === selectedType)?.description}
                  </p>
                )}
              </div>
              <button
                onClick={handleAddWidget}
                disabled={!selectedType}
                className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <FaPlus size={14} />
                Add Widget
              </button>
            </div>
          </div>

          {/* Existing Widgets Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Existing Widgets</h3>
            <div className="space-y-3">
              {availableWidgetTypes
                .filter(type => !type.adminOnly || admin)
                .map(type => {
                  const enabledWidgets = widgets.filter(w => w.type === type.type && w.enabled)
                  const disabledWidgets = widgets.filter(w => w.type === type.type && !w.enabled)
                  const allWidgets = [...enabledWidgets, ...disabledWidgets]

                  if (allWidgets.length === 0) return null

                  return (
                    <div key={type.type} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">{type.title}</h4>
                          <p className="text-sm text-gray-600">{type.description}</p>
                        </div>
                        <div className="text-sm text-gray-500">
                          {enabledWidgets.length} enabled, {disabledWidgets.length} disabled
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        {allWidgets.map(widget => (
                          <div key={widget.id} className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded">
                            <span className="text-sm text-gray-900">
                              {widget.title}
                              {!widget.enabled && <span className="text-gray-500 ml-2">(Hidden)</span>}
                            </span>
                            <button
                              onClick={() => onToggleWidget(widget.id)}
                              className={`p-1 rounded ${
                                widget.enabled 
                                  ? 'text-green-600 hover:text-green-800' 
                                  : 'text-gray-400 hover:text-gray-600'
                              }`}
                              title={widget.enabled ? 'Hide widget' : 'Show widget'}
                            >
                              {widget.enabled ? <FaEye size={14} /> : <FaEyeSlash size={14} />}
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )
                })}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}
