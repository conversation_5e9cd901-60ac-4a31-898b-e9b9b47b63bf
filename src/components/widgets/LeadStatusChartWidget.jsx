"use client"

import { Bar } from "react-chartjs-2"

export default function LeadStatusChartWidget({ chartData, leadStatuses = [], statusColors = {}, options = {} }) {
  // Provide default chart data if not available
  const defaultChartData = {
    labels: [],
    datasets: [
      {
        label: "Lead Status Overview",
        data: [],
        backgroundColor: [],
      },
    ],
  }

  const safeChartData = chartData && chartData.labels ? chartData : defaultChartData

  return (
    <div>
      <div className="flex flex-col lg:flex-row lg:items-center justify-between">
        <h2 className="text-lg font-bold mb-4 lg:mb-0 text-gray-900">Lead Status Overview</h2>
        <div className="flex flex-wrap gap-4 text-sm text-gray-700">
          {leadStatuses.map(status => (
            <div key={status} className="flex items-center gap-2">
              <span className="block w-4 h-4 rounded-sm" style={{ backgroundColor: statusColors.leads?.[status] || "#6b7280" }} />
              <span>{status}</span>
            </div>
          ))}
        </div>
      </div>
      <div className="mt-4 h-80">
        {safeChartData.labels.length > 0 ? (
          <Bar
            data={safeChartData}
            options={{
              ...options,
              maintainAspectRatio: false,
              responsive: true,
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>Loading chart data...</p>
          </div>
        )}
      </div>
    </div>
  )
}
