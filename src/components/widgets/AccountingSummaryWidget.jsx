"use client"

import { useUser } from "@/hooks/useUser"
import { useSelector } from "react-redux"

export default function AccountingSummaryWidget({
  invoicedTotal,
  grossProfit,
  adminCosts,
  netIncome,
  adminCostPercentage,
  invoicedTimeRange,
  invoicedStartDate,
  invoicedEndDate,
  onTimeRangeChange,
  onStartDateChange,
  onEndDateChange,
  onAdminCostPercentageChange
}) {
  const { admin } = useUser()
  const { user } = useSelector(state => state.auth)

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-gray-900">Accounting Summary</h2>
        <div className="flex items-center gap-4">
          <select
            value={invoicedTimeRange}
            onChange={e => onTimeRangeChange(e.target.value)}
            className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
          >
            <option value="ytd">Year to Date</option>
            <option value="7days">Last 7 Days</option>
            <option value="30days">Last 30 Days</option>
            <option value="quarter">This Quarter</option>
            <option value="custom">Custom Range</option>
          </select>
          {invoicedTimeRange === "custom" && (
            <div className="flex items-center gap-2">
              <input
                type="date"
                value={invoicedStartDate}
                onChange={e => onStartDateChange(e.target.value)}
                className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              />
              <span className="text-gray-900">to</span>
              <input
                type="date"
                value={invoicedEndDate}
                onChange={e => onEndDateChange(e.target.value)}
                className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              />
            </div>
          )}
          {admin && (
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-600">Admin Cost %:</label>
              <select
                value={adminCostPercentage}
                onChange={e => onAdminCostPercentageChange(Number(e.target.value))}
                className="border border-gray-300 rounded p-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
              >
                {[5, 10, 15, 20, 25, 30].map(percent => (
                  <option key={percent} value={percent}>
                    {percent}%
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {admin ? (
          <>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-600">Total Invoiced</h3>
              <p className="text-2xl font-bold text-gray-900">
                ${invoicedTotal.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-600">Gross Profit</h3>
              <p className="text-2xl font-bold text-gray-900">
                ${grossProfit.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-600">Administrative Costs ({adminCostPercentage}%)</h3>
              <p className="text-2xl font-bold text-gray-900">
                ${adminCosts.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-600">Net Income</h3>
              <p className="text-2xl font-bold text-gray-900">
                ${netIncome.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            </div>
          </>
        ) : (
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-sm font-medium text-gray-600">Your Commission ({Math.round((user?.commissionRate || 0) * 100)}%)</h3>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              ${(invoicedTotal * (user?.commissionRate || 0)).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
