"use client"

import { FaFileExport } from "react-icons/fa"

export default function InvoicedJobsWidget({
  filteredInvoicedJobs,
  jobChangeOrders,
  getDateFromTimestamp,
  exportToCSV
}) {
  const handleExport = () => {
    const exportData = filteredInvoicedJobs.map(job => ({
      "Job ID": job.displayId || "",
      Title: job.name || "",
      Customer: job.customer?.name || "",
      "Invoiced Date": getDateFromTimestamp(job.invoicedAt)?.toLocaleDateString() || "",
      "Total Amount": (() => {
        const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0
        const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
          .filter(co => co.status?.toLowerCase() === "approved")
          .reduce(
            (sum, co) =>
              sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
            0,
          )
        return (servicesTotal + changeOrdersTotal).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      })(),
      Profit: (() => {
        const jobServicesProfit = (job.services || []).reduce(
          (sum, service) => sum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
          0,
        )
        const changeOrdersProfit = (jobChangeOrders?.[job.id] || [])
          .filter(co => co.approvedBy)
          .reduce(
            (sum, co) =>
              sum +
              (co.services || []).reduce(
                (coSum, service) => coSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
                0,
              ),
            0,
          )
        return (jobServicesProfit + changeOrdersProfit).toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })
      })(),
    }))
    exportToCSV(exportData, "invoiced-jobs")
  }

  const calculateJobTotal = (job) => {
    const servicesTotal = job.services?.reduce((sum, service) => sum + (service.sell || 0) * (service.quantity || 1), 0) || 0
    const changeOrdersTotal = (jobChangeOrders?.[job.id] || [])
      .filter(co => co.status?.toLowerCase() === "approved")
      .reduce(
        (sum, co) =>
          sum + (co.services || []).reduce((coSum, service) => coSum + (service.sell || 0) * (service.quantity || 1), 0),
        0,
      )
    return servicesTotal + changeOrdersTotal
  }

  const calculateJobProfit = (job) => {
    const jobServicesProfit = (job.services || []).reduce(
      (sum, service) => sum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
      0,
    )
    const changeOrdersProfit = (jobChangeOrders?.[job.id] || [])
      .filter(co => co.approvedBy)
      .reduce(
        (sum, co) =>
          sum +
          (co.services || []).reduce(
            (coSum, service) => coSum + ((service.sell || 0) - (service.cost || 0)) * (service.quantity || 1),
            0,
          ),
        0,
      )
    return jobServicesProfit + changeOrdersProfit
  }

  const totalAmount = filteredInvoicedJobs.reduce((sum, job) => sum + calculateJobTotal(job), 0)
  const totalProfit = filteredInvoicedJobs.reduce((sum, job) => sum + calculateJobProfit(job), 0)

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold text-gray-900">Invoiced Jobs Summary</h2>
        <button
          onClick={handleExport}
          className="flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          <FaFileExport /> Export CSV
        </button>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="border p-2 text-gray-900">Job ID</th>
              <th className="border p-2 text-gray-900">Title</th>
              <th className="border p-2 text-gray-900">Customer</th>
              <th className="border p-2 text-gray-900">Invoiced Date</th>
              <th className="border p-2 text-gray-900">Total Amount</th>
              <th className="border p-2 text-gray-900">Profit</th>
            </tr>
          </thead>
          <tbody>
            {filteredInvoicedJobs.map(job => (
              <tr key={job.id}>
                <td className="border p-2 text-gray-800">
                  <a href={`/jobs/${job.id}`} className="text-blue-500 hover:text-blue-700 underline hover:no-underline">
                    {job.displayId}
                  </a>
                </td>
                <td className="border p-2 text-gray-800">{job.name}</td>
                <td className="border p-2 text-gray-800">{job.customer?.name}</td>
                <td className="border p-2 text-gray-800">{getDateFromTimestamp(job.invoicedAt)?.toLocaleDateString()}</td>
                <td className="border p-2 text-gray-800">
                  ${calculateJobTotal(job).toLocaleString("en-US", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </td>
                <td className="border p-2 text-gray-800">
                  ${calculateJobProfit(job).toLocaleString("en-US", {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}
                </td>
              </tr>
            ))}
            {/* Subtotal row */}
            <tr className="bg-gray-50 font-semibold">
              <td colSpan="4" className="border p-2 text-gray-800 text-right">
                Subtotal:
              </td>
              <td className="border p-2 text-gray-800">
                ${totalAmount.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </td>
              <td className="border p-2 text-gray-800">
                ${totalProfit.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  )
}
