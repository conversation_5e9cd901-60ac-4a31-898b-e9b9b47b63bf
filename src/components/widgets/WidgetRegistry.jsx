"use client"

import AccountingSummaryWidget from "./AccountingSummaryWidget"
import CompletedJobsWidget from "./CompletedJobsWidget"
import InvoicedJobsWidget from "./InvoicedJobsWidget"
import JobStatusChartWidget from "./JobStatusChartWidget"
import OpportunityStatusChartWidget from "./OpportunityStatusChartWidget"
import LeadStatusChartWidget from "./LeadStatusChartWidget"
import JobsTableWidget from "./JobsTableWidget"
import OpportunitiesTableWidget from "./OpportunitiesTableWidget"
import LeadsTableWidget from "./LeadsTableWidget"

// Widget registry mapping widget types to components
export const WIDGET_COMPONENTS = {
  AccountingSummary: AccountingSummaryWidget,
  CompletedJobs: CompletedJobsWidget,
  InvoicedJobs: InvoicedJobsWidget,
  JobStatusChart: JobStatusChartWidget,
  OpportunityStatusChart: OpportunityStatusChartWidget,
  LeadStatusChart: LeadStatusChartWidget,
  JobsTable: JobsTableWidget,
  OpportunitiesTable: OpportunitiesTableWidget,
  LeadsTable: LeadsTableWidget,
}

// Helper function to get widget component
export function getWidgetComponent(type) {
  return WIDGET_COMPONENTS[type] || null
}

// Helper function to render a widget
export function renderWidget(widget, props) {
  const WidgetComponent = getWidgetComponent(widget.type)
  
  if (!WidgetComponent) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>Widget type "{widget.type}" not found</p>
      </div>
    )
  }

  return <WidgetComponent {...props} />
}
