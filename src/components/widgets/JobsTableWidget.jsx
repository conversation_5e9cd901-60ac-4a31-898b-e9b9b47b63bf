"use client"

import StatusPickerWithColor from "@/components/StatusPickerWithColor"
import DatePicker from "@/components/DatePicker"

export default function JobsTableWidget({
  filteredJobs = [],
  searchQuery = "",
  statusFilter = "All",
  startDate = "",
  endDate = "",
  jobStatuses = [],
  onSearchChange,
  onStatusFilterChange,
  onStartDateChange,
  onEndDateChange,
  onStatusChange,
  onDueDateChange,
  getDateFromTimestamp,
}) {
  return (
    <div>
      {/* Job Filters */}
      <div className="bg-gray-50 p-4 rounded mb-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <input
          type="text"
          placeholder="Search by job title"
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
          className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
        />
        <select
          value={statusFilter}
          onChange={e => onStatusFilterChange(e.target.value)}
          className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
        >
          <option value="All">All Statuses</option>
          {jobStatuses.map(status => (
            <option key={status} value={status}>
              {status}
            </option>
          ))}
        </select>
        <input
          type="date"
          placeholder="Start Date"
          value={startDate}
          onChange={e => onStartDateChange(e.target.value)}
          className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
        />
        <input
          type="date"
          placeholder="End Date"
          value={endDate}
          onChange={e => onEndDateChange(e.target.value)}
          className="border border-gray-300 rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
        />
      </div>

      {/* Jobs Table */}
      <div className="overflow-x-auto">
        {filteredJobs.length > 0 ? (
          <table className="w-full border-collapse bg-white">
            <thead>
              <tr className="bg-gray-50">
                <th className="border p-2 text-left text-gray-900">Job ID</th>
                <th className="border p-2 text-left text-gray-900">Title</th>
                <th className="border p-2 text-left text-gray-900">Customer</th>
                <th className="border p-2 text-left text-gray-900">Due Date</th>
                <th className="border p-2 text-left text-gray-900">Status</th>
              </tr>
            </thead>
            <tbody>
              {filteredJobs.map(job => (
                <tr key={job.id} className="hover:bg-gray-50">
                  <td className="border p-2 text-gray-800">
                    <a href={`/jobs/${job.id}`} className="text-blue-500 hover:text-blue-700 underline hover:no-underline">
                      {job.displayId}
                    </a>
                  </td>
                  <td className="border p-2 text-gray-800">{job.name}</td>
                  <td className="border p-2 text-gray-800">{job.customer?.name}</td>
                  <td className="border p-2 text-gray-800 whitespace-nowrap text-center">
                    <div className="flex justify-center">
                      <DatePicker
                        value={job.dueDate ? getDateFromTimestamp(job.dueDate)?.toISOString().split("T")[0] : ""}
                        onChange={newDate => onDueDateChange(job.id, newDate)}
                        isEditing={true}
                      />
                    </div>
                  </td>
                  <td className="border p-2">
                    <StatusPickerWithColor
                      type="jobs"
                      currentStatus={job?.status}
                      onStatusChange={newStatus => onStatusChange(job.id, newStatus)}
                      className="w-full"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-gray-600 text-center py-8">No jobs found. Try adjusting your filters.</p>
        )}
      </div>
    </div>
  )
}
