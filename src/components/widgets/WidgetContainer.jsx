"use client"

import { useState } from "react"
import { FaCog, FaTimes, FaGripVertical } from "react-icons/fa"

export default function WidgetContainer({ 
  widget, 
  children, 
  onRemove, 
  onConfigure, 
  isDragging = false,
  dragHandleProps = {}
}) {
  const [showControls, setShowControls] = useState(false)

  const getSizeClass = (size) => {
    switch (size) {
      case "quarter":
        return "col-span-1 md:col-span-1 lg:col-span-3"
      case "half":
        return "col-span-1 md:col-span-1 lg:col-span-6"
      case "three-quarter":
        return "col-span-1 md:col-span-2 lg:col-span-9"
      case "full":
      default:
        return "col-span-1 md:col-span-2 lg:col-span-12"
    }
  }

  return (
    <div 
      className={`${getSizeClass(widget.size)} ${isDragging ? 'opacity-50' : ''}`}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      <div className="bg-white rounded-lg shadow-md relative group">
        {/* Widget Controls */}
        {showControls && (
          <div className="absolute top-2 right-2 z-10 flex gap-2">
            {/* Drag Handle */}
            <button
              {...dragHandleProps}
              className="p-1 text-gray-400 hover:text-gray-600 cursor-move"
              title="Drag to reorder"
            >
              <FaGripVertical size={12} />
            </button>
            
            {/* Configure Button */}
            {onConfigure && (
              <button
                onClick={() => onConfigure(widget)}
                className="p-1 text-gray-400 hover:text-blue-600"
                title="Configure widget"
              >
                <FaCog size={12} />
              </button>
            )}
            
            {/* Remove Button */}
            {onRemove && (
              <button
                onClick={() => onRemove(widget.id)}
                className="p-1 text-gray-400 hover:text-red-600"
                title="Remove widget"
              >
                <FaTimes size={12} />
              </button>
            )}
          </div>
        )}

        {/* Widget Content */}
        <div className="p-4">
          {children}
        </div>
      </div>
    </div>
  )
}
