"use client"

import { useState } from "react"
import { FaCog, FaTimes, FaGripVertical } from "react-icons/fa"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

export default function WidgetContainer({ widget, children, onRemove, onConfigure }) {
  const [showControls, setShowControls] = useState(false)

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: widget.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const getSizeClass = size => {
    switch (size) {
      case "quarter":
        return "col-span-1 md:col-span-1 lg:col-span-3"
      case "half":
        return "col-span-1 md:col-span-1 lg:col-span-6"
      case "three-quarter":
        return "col-span-1 md:col-span-2 lg:col-span-9"
      case "full":
      default:
        return "col-span-1 md:col-span-2 lg:col-span-12"
    }
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${getSizeClass(widget.size)} ${isDragging ? "opacity-50 z-50" : ""}`}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      <div className="bg-white rounded-lg shadow-md relative group transition-shadow hover:shadow-lg">
        {/* Widget Controls */}
        {showControls && (
          <div className="absolute top-2 right-2 z-10 flex gap-2">
            {/* Drag Handle */}
            <button
              {...attributes}
              {...listeners}
              className="p-1 text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing"
              title="Drag to reorder"
            >
              <FaGripVertical size={12} />
            </button>

            {/* Configure Button */}
            {onConfigure && (
              <button onClick={() => onConfigure(widget)} className="p-1 text-gray-400 hover:text-blue-600" title="Configure widget">
                <FaCog size={12} />
              </button>
            )}

            {/* Remove Button */}
            {onRemove && (
              <button onClick={() => onRemove(widget.id)} className="p-1 text-gray-400 hover:text-red-600" title="Remove widget">
                <FaTimes size={12} />
              </button>
            )}
          </div>
        )}

        {/* Widget Content */}
        <div className="p-4">{children}</div>
      </div>
    </div>
  )
}
