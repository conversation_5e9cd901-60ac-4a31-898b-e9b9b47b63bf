"use client"

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core"
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from "@dnd-kit/sortable"
import WidgetContainer from "./WidgetContainer"
import { renderWidget } from "./WidgetRegistry"

export default function DraggableWidgetGrid({
  widgets,
  onReorderWidgets,
  onRemoveWidget,
  onConfigureWidget,
  getWidgetProps
}) {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  function handleDragEnd(event) {
    const { active, over } = event

    if (active.id !== over?.id) {
      const oldIndex = widgets.findIndex(widget => widget.id === active.id)
      const newIndex = widgets.findIndex(widget => widget.id === over.id)

      const newOrder = arrayMove(widgets, oldIndex, newIndex)
      onReorderWidgets(newOrder)
    }
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={widgets.map(w => w.id)} strategy={rectSortingStrategy}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-6">
          {widgets.map(widget => (
            <WidgetContainer
              key={widget.id}
              widget={widget}
              onRemove={onRemoveWidget}
              onConfigure={onConfigureWidget}
            >
              {renderWidget(widget, getWidgetProps(widget))}
            </WidgetContainer>
          ))}
        </div>
      </SortableContext>
    </DndContext>
  )
}
