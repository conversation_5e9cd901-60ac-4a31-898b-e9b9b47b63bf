import React, { useEffect, useState, useMemo } from "react"
import { collection, getDocs, doc, getDoc, updateDoc, setDoc } from "firebase/firestore"
import { HiChevronDown, HiChevronUp, HiCheck, HiX, HiPencilAlt } from "react-icons/hi"
import { FaTrashAlt, <PERSON>a<PERSON><PERSON>, <PERSON>aCheck } from "react-icons/fa"
import { MdRequestQuote, MdAssignment, MdReceipt } from "react-icons/md"
import { useCompany } from "@/hooks/useCompany"
import { useUser } from "@/hooks/useUser"
import { useApprovalLimits } from "@/context/ApprovalLimitsContext"
import { calculateSubtotal } from "@/utils/calculateSubtotal"
import { generateInvoices } from "@/utils/generateInvoiceUtil"
import { useSelector } from "react-redux"
import { useInvoices } from "@/hooks/useInvoices"
import { useCSAs } from "@/hooks/useCSAs"
import { useQuotes } from "@/hooks/useQuotes"
import { db } from "@/firebase"
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { generateChangeOrderQuotePDF2 } from "@/utils/generateChangeOrderQuotePDF2"
import { getNextDisplayId } from "@/utils/getNextDisplayId"
import { Timestamp } from "firebase/firestore"
// import { generateQuotePDF } from "@/utils/generateQuotePDF"
import { generateJobQuotePDF } from "@/utils/generateJobQuotePDF"

// FRANK: add due & payment dates

const JobServicesTable = ({ job, globalSubcontractor }) => {
  // const [assignedSubcontractors, setAssignedSubcontractors] = useState({})
  // const [pdfUrl, setPdfUrl] = useState("") // Turning off preview - 4/13/25 Frank
  // const [templateContent, setTemplateContent] = useState("")
  const [selectedServices, setSelectedServices] = useState([])
  const [editedServices, setEditedServices] = useState(job.services)
  const [marginUnit, setMarginUnit] = useState("%")
  const [expandedRow, setExpandedRow] = useState(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [pdfUrls, setPdfUrls] = useState([])
  const [invoiceUrl, setInvoiceUrl] = useState(null)
  const [currentPage, setCurrentPage] = useState(0)
  const { companyLogo, companyName } = useCompany()
  const { companyId } = useSelector(state => state.company)
  const [templates, setTemplates] = useState("")
  const [isEditing, setIsEditing] = useState(false)
  const { csas } = useCSAs(companyId, job.id)
  const { invoices } = useInvoices(companyId, job.id)
  const { quotes } = useQuotes(companyId, job.id)

  const { admin } = useUser()
  const { approvalLimits } = useApprovalLimits()

  const [isGeneratingCSA, setIsGeneratingCSA] = useState(false)
  const [isGeneratingInvoice, setIsGeneratingInvoice] = useState(false)
  const [isGeneratingQuote, setIsGeneratingQuote] = useState(false)

  useEffect(() => {
    if (job?.services) {
      // Ensure all quantities are valid numbers
      const servicesWithValidQuantities = job.services.map(service => ({
        ...service,
        quantity: typeof service.quantity === "number" ? service.quantity : service.quantity === "" ? 1 : parseFloat(service.quantity) || 1,
      }))
      setEditedServices(servicesWithValidQuantities)
    }
  }, [job])

  const handleFieldChange = (index, field, value) => {
    setEditedServices(prev => {
      const updated = [...prev]
      if (field === "cost") {
        // Keep the value as a string to allow decimal input
        updated[index] = {
          ...updated[index],
          [field]: value,
        }

        // If user has set a margin, recalculate sell based on the new cost and stored margin
        if (updated[index].marginValue !== undefined && updated[index].marginValue !== null && updated[index].marginValue !== "") {
          const cost = Number(value || 0)
          const marginValue = parseFloat(String(updated[index].marginValue).replace(/[%$]/g, ""))

          if (!isNaN(marginValue) && !isNaN(cost)) {
            // Use the stored margin unit if available, otherwise use the current global setting
            const unit = updated[index].marginUnit || marginUnit

            if (unit === "%") {
              updated[index].sell = (cost * (1 + marginValue / 100)).toFixed(2)
            } else {
              updated[index].sell = (cost + marginValue).toFixed(2)
            }
          }
        }
      } else if (field === "sell") {
        // Keep the value as a string to allow decimal input
        updated[index] = {
          ...updated[index],
          [field]: value,
        }

        // If the user manually changes the sell value, clear any stored margin
        // so it will be recalculated based on cost and sell
        if (updated[index].marginValue !== undefined && updated[index].marginValue !== null) {
          updated[index].marginValue = null
          updated[index].marginUnit = null
        }
      } else if (field === "quantity") {
        // Always convert quantity to number, default to 1
        const numValue = parseFloat(value)
        updated[index] = {
          ...updated[index],
          [field]: isNaN(numValue) ? 1 : Math.max(1, numValue), // Ensure quantity is at least 1
        }
      } else {
        // Handle other fields normally
        updated[index] = {
          ...updated[index],
          [field]: value,
        }
      }
      return updated
    })
  }

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const templatesRef = collection(db, `companies/${companyId}/templates`)
        const querySnapshot = await getDocs(templatesRef)

        // Get all templates and their data
        const fetchedTemplates = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        }))

        // Set the templates state
        setTemplates(fetchedTemplates)
      } catch (err) {
        console.error("Error fetching templates:", err.message)
      }
    }

    fetchTemplates()
  }, [companyId])

  useEffect(() => {
    if (globalSubcontractor) {
      // Don't automatically select all services
      // Just keep the current selection
    } else {
      // Reset all assignments and clear selected services
      setSelectedServices([])
    }
  }, [globalSubcontractor, editedServices])

  function calculateMargin(cost, sell, unit = "%", quantity = 1) {
    try {
      // Debug logging for problematic values
      if (cost === undefined || sell === undefined) {
        console.warn("Undefined values in calculateMargin:", { cost, sell, quantity })
        return unit === "%" ? "0.00%" : "$0.00"
      }

      // Convert inputs to numbers, defaulting to 0 if invalid
      const costNum = typeof cost === "number" ? cost : parseFloat(String(cost).replace(/[^0-9.-]/g, "")) || 0
      const sellNum = typeof sell === "number" ? sell : parseFloat(String(sell).replace(/[^0-9.-]/g, "")) || 0
      const qty = typeof quantity === "number" ? quantity : parseFloat(String(quantity).replace(/[^0-9.-]/g, "")) || 1

      if (unit === "%") {
        // For percentage margin, we don't need to multiply by quantity
        // since the percentage should be the same regardless of quantity
        if (costNum === 0) {
          if (sellNum > 0) {
            return "100.00%"
          } else if (sellNum === 0) {
            return "0.00%"
          } else {
            return "-100.00%"
          }
        }

        const marginPercent = ((sellNum - costNum) / Math.abs(costNum)) * 100
        const formattedMargin = marginPercent.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })

        // Debug logging for final calculation
        if (isNaN(marginPercent)) {
          console.warn("NaN in final margin calculation:", {
            costNum,
            sellNum,
            marginPercent,
            formattedMargin,
          })
          return "0.00%"
        }

        return formattedMargin + "%"
      } else if (unit === "$") {
        // For dollar margin, we do need to multiply by quantity
        const marginDollar = (sellNum - costNum) * qty
        return "$" + marginDollar.toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })
      }

      throw new Error("Invalid unit provided. Use '%' or '$'.")
    } catch (error) {
      console.error("Error in calculateMargin:", error)
      return unit === "%" ? "0.00%" : "$0.00"
    }
  }

  const toggleRowSelection = rowId => {
    if (!globalSubcontractor?.id) {
      alert("Please assign a global subcontractor before selecting services.")
      return
    }
    setSelectedServices(prev => (prev.includes(rowId) ? prev.filter(id => id !== rowId) : [...prev, rowId]))
  }

  const toggleSelectAll = () => {
    if (!globalSubcontractor?.id) {
      alert("Please assign a global subcontractor before selecting services.")
      return
    }
    if (selectedServices.length === editedServices.length) {
      setSelectedServices([])
    } else {
      setSelectedServices(editedServices.map(service => service.id))
    }
  }

  // For line item subcontractors
  // const handleAssignSubcontractor = (serviceId, subcontractor) => {
  //   setAssignedSubcontractors(prev => {
  //     const updated = { ...prev }
  //     if (!subcontractor) {
  //       delete updated[serviceId]
  //     } else {
  //       updated[serviceId] = subcontractor
  //     }
  //     return updated
  //   })
  // }

  const toggleRowExpansion = index => {
    setExpandedRow(prev => (prev === index ? null : index))
  }

  const handleSave = async () => {
    if (!companyId || !job?.id) return

    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, job.id)

      // Convert numeric fields to numbers while preserving empty strings
      const formattedServices = editedServices.map(service => ({
        ...service,
        cost: service.cost === "" ? "" : Number(service.cost),
        sell: service.sell === "" ? "" : Number(service.sell),
        quantity: service.quantity === "" ? "" : Number(service.quantity),
        // Preserve the margin value and unit if they exist
        marginValue: service.marginValue || null,
        marginUnit: service.marginUnit || null,
      }))
      console.log("formattedServices", formattedServices)
      await updateDoc(docRef, { services: formattedServices })
      setIsEditing(false)
    } catch (err) {
      console.error(`Error updating job:`, err.message)
      alert(`Failed to update job. Please try again.`)
    }
  }

  const handleCancel = () => {
    setEditedServices(job.services)
    setIsEditing(false)
  }

  const removeService = async (e, serviceId) => {
    e.preventDefault()
    if (!confirm(`Are you sure you want to delete this service from the job?`)) return
    try {
      const docRef = doc(db, `companies/${companyId}/jobs`, job.id)
      await updateDoc(docRef, { services: editedServices.filter(service => service.id !== serviceId) })
      setIsEditing(false)
    } catch (err) {
      console.error(`Error updating ${type}:`, err.message)
      alert(`Failed to update ${type}. Please try again.`)
    }
  }

  if (!job || !job.services) {
    return <p className="text-gray-500">No job data available.</p>
  }

  const createCSA = async () => {
    try {
      setIsGeneratingCSA(true)
      // Get only the selected services
      const selectedServicesData = job.services.filter(service => selectedServices.includes(service.id))

      // Get the job's services and format them
      const jobWithServices = {
        ...job,
        services: selectedServicesData.map(service => ({
          name: service.name || "",
          quantity: service.quantity || 1,
          price: service.cost || 0,
          total: (service.quantity || 1) * (service.cost || 0),
          description: service.description || "",
        })),
        projectId: job.displayId || job.id,
        clientName: job.customer?.name || "",
      }

      // Fetch change orders from Firebase
      const changeOrdersRef = collection(db, `companies/${companyId}/jobs/${job.id}/changeOrders`)
      const changeOrdersSnapshot = await getDocs(changeOrdersRef)
      const changeOrders = changeOrdersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))

      // Filter and format approved change orders
      const approvedChangeOrders = changeOrders
        .filter(order => order.status?.toLowerCase() === "approved")
        .map(order => ({
          services: order.services.map(service => ({
            name: service.name || "",
            quantity: service.quantity || 1,
            sell: service.sell || 0,
            total: (service.quantity || 1) * (service.sell || 0),
            description: service.description || "",
          })),
          notes: order.notes || [],
          ...order,
        }))
        .sort((a, b) => {
          // Sort by creation date, oldest first
          const dateA = a.createdAt?.seconds || 0
          const dateB = b.createdAt?.seconds || 0
          return dateA - dateB
        })

      // Generate the PDF with all change orders
      const pdfUrl = await generateChangeOrderQuotePDF2(
        jobWithServices,
        approvedChangeOrders,
        {
          companyName,
          companyLogo,
          id: companyId,
        },
        templates.find(template => template.type === "CSA")?.content || "",
      )

      // Format services to include IDs and maintain original service data
      const formattedServices = [...selectedServicesData, ...approvedChangeOrders.flatMap(order => order.services)].map(service => ({
        ...service,
        id: service.id || crypto.randomUUID(), // Ensure each service has an ID
        name: service.name || "",
        quantity: service.quantity || 1,
        cost: service.cost || 0,
        total: (service.quantity || 1) * (service.cost || 0),
        description: service.description || "",
      }))

      // Calculate subtotal
      const subtotal = formattedServices.reduce((sum, service) => sum + (service.cost || 0) * (service.quantity || 1), 0)

      // Create a new CSA document in Firestore and get displayId
      const csasCollectionRef = collection(db, `companies/${companyId}/jobs/${job.id}/csas`)
      const newCsaRef = doc(csasCollectionRef)
      const displayId = await getNextDisplayId("csas", companyId)

      // Upload the PDF to Firebase Storage with a more specific path
      const storage = getStorage()
      const pdfRef = ref(storage, `companies/${companyId}/jobs/${job.id}/csas/${displayId}.pdf`)
      const response = await fetch(pdfUrl)
      const blob = await response.blob()
      await uploadBytes(pdfRef, blob)
      const downloadUrl = await getDownloadURL(pdfRef)

      await setDoc(newCsaRef, {
        id: newCsaRef.id,
        displayId,
        pdfUrl: downloadUrl,
        services: formattedServices,
        subcontractor: job.subcontractor || globalSubcontractor,
        subtotal,
        createdAt: Timestamp.now(),
      })

      // Update the job's services to mark them as having a CSA
      const jobRef = doc(db, `companies/${companyId}/jobs`, job.id)
      await updateDoc(jobRef, {
        services: job.services.map(service => ({
          ...service,
          csaGenerated: selectedServices.includes(service.id) ? true : service.csaGenerated,
          csaId: selectedServices.includes(service.id) ? newCsaRef.id : service.csaId,
        })),
      })

      console.log("CSA generated and uploaded successfully")
    } catch (error) {
      console.error("Error generating CSA:", error)
      alert("Failed to generate CSA. Please try again.")
    } finally {
      setIsGeneratingCSA(false)
    }
  }

  const createInvoice = async (service, singlePDF = false) => {
    console.log("Creating invoice...")
    setIsGeneratingInvoice(true)

    try {
      // Get approved change orders if invoicing whole job
      let servicesToInvoice = singlePDF ? job.services : [service]
      if (singlePDF && job.changeOrders) {
        const approvedChangeOrderServices = job.changeOrders
          .filter(order => order.status?.toLowerCase() === "approved")
          .flatMap(order => order.services)

        // Combine original services with approved change order services
        servicesToInvoice = [...servicesToInvoice, ...approvedChangeOrderServices]
      }

      await generateInvoices({
        job,
        selectedServices: servicesToInvoice,
        globalSubcontractor,
        companyId,
        templates,
        companyLogo,
        companyName,
        singlePDF,
      })
    } finally {
      setIsGeneratingInvoice(false)
    }
  }

  const handleMarkCompleted = async serviceId => {
    if (!serviceId) {
      console.error("Missing required parameters to mark work as completed.")
      return
    }

    try {
      const jobRef = doc(db, `companies/${companyId}/jobs`, job.id)

      // Fetch the current services array
      const jobSnap = await getDoc(jobRef)
      if (!jobSnap.exists()) {
        console.error("Job not found.")
        return
      }

      const jobData = jobSnap.data()
      const updatedServices = jobData.services.map(service => (service.id === serviceId ? { ...service, workCompleted: true } : service))

      // Update the job document with the modified services array
      await updateDoc(jobRef, { services: updatedServices })

      console.log(`Service ${serviceId} marked as completed.`)
    } catch (error) {
      console.error("Error marking work as completed:", error.message)
    }
  }

  const isDisabled = (!admin && !globalSubcontractor) || selectedServices.length === 0

  const subtotal = calculateSubtotal(editedServices)

  const requiresApproval = useMemo(() => {
    if (!approvalLimits) return false
    const { job: jobLimits } = approvalLimits
    const { total } = jobLimits
    if (total) {
      return subtotal > total
    }
    return false
  }, [approvalLimits, job, subtotal])

  function calculatePercentage(arr, booleanKey) {
    if (!Array.isArray(arr) || arr.length === 0) return "0.00%" // Avoid division by zero

    const count = arr.reduce((acc, obj) => acc + (obj[booleanKey] === true ? 1 : 0), 0)
    const percentage = (count / arr.length) * 100
    return percentage.toFixed(2) + "%" // Format with exactly 2 decimal places
  }

  const isLocked = service => {
    if (admin) return false
    if (service?.workCompleted) return true
    if (service?.invoiceGenerated) return true
    return false
  }

  const handleMarginChange = (index, value) => {
    setEditedServices(prev => {
      const updated = [...prev]

      // Get cost from service.cost
      const cost = Number(updated[index].cost || 0)

      // Store both the value and the unit used when setting it
      updated[index] = {
        ...updated[index],
        marginValue: value,
        marginUnit: marginUnit, // Store the current unit when margin is set
      }

      // If the margin field has a value and is a number, update sell accordingly.
      if (value !== "" && !isNaN(Number(value))) {
        if (marginUnit === "%") {
          // Ensure value is a string before using replace
          const valueStr = String(value)
          // Remove % symbol if present and convert to number
          const marginPercent = parseFloat(valueStr.replace("%", ""))
          updated[index].sell = (cost * (1 + marginPercent / 100)).toFixed(2)
        } else {
          // Ensure value is a string before using replace
          const valueStr = String(value)
          // Remove $ symbol if present and convert to number
          const marginDollar = parseFloat(valueStr.replace("$", ""))
          updated[index].sell = (cost + marginDollar).toFixed(2)
        }
      }
      return updated
    })
  }

  const createQuote = async () => {
    try {
      setIsGeneratingQuote(true)
      const quoteId = await getNextDisplayId("quote", companyId)
      // Get only the selected services
      const selectedServicesData = job.services.filter(service => selectedServices.includes(service.id))

      // Format line items for PDF
      const lineItems = selectedServicesData.map(service => ({
        id: service.id,
        name: service.name || "N/A",
        description: service.description?.replace(/\n/g, " ") || "No description available.",
        cost: parseFloat(service.cost || 0).toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        sell: parseFloat(service.sell || 0).toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        subtotal: (parseFloat(service.sell || 0) * (service.quantity || 1)).toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        }),
        quantity: service.quantity || 1,
      }))

      // Generate the PDF
      const pdfData = {
        companyLogoURL: companyLogo || "",
        companyName: companyName || "Your Company",
        quoteId,
        jobDetails: {
          projectId: job.displayId || "TBD",
          projectName: job.name || "Unnamed Project",
          location: job.location || "No location specified",
          contactName: job.customer?.name || "No contact name",
          contactEmail: job.customer?.email || "No contact email",
          contactPhone: job.customer?.phone || "No contact phone",
          workOrderId: job.workOrderId || "N/A",
          customerName: job.customer?.name || "N/A",
        },
        lineItems,
        subtotal: calculateSubtotal(lineItems),
        notes: job.notes?.map(note => note.text) || [],
        selectedTemplateContent: templates.find(template => template.type === "Quote")?.content || "",
      }

      const pdfBlob = await generateJobQuotePDF(pdfData)

      // Create a new quote document in Firestore
      const quotesCollectionRef = collection(db, `companies/${companyId}/jobs/${job.id}/quotes`)
      const newQuoteRef = doc(quotesCollectionRef)
      const displayId = await getNextDisplayId("quotes", companyId)

      // Upload the PDF to Firebase Storage
      const storage = getStorage()
      const pdfRef = ref(storage, `companies/${companyId}/jobs/${job.id}/quotes/${displayId}.pdf`)
      await uploadBytes(pdfRef, pdfBlob)
      const downloadUrl = await getDownloadURL(pdfRef)

      // Create the quote document
      const newQuote = {
        id: newQuoteRef.id,
        displayId,
        pdfUrl: downloadUrl,
        services: selectedServicesData,
        subtotal: calculateSubtotal(selectedServicesData),
        createdAt: Timestamp.now(),
      }

      await setDoc(newQuoteRef, newQuote)

      // Update the job's services to mark them as having a quote
      const jobRef = doc(db, `companies/${companyId}/jobs`, job.id)
      await updateDoc(jobRef, {
        services: job.services.map(service => ({
          ...service,
          quoteGenerated: selectedServices.includes(service.id) ? true : service.quoteGenerated,
          quoteId: selectedServices.includes(service.id) ? newQuoteRef.id : service.quoteId,
        })),
      })

      console.log("Quote generated and uploaded successfully")
    } catch (error) {
      console.error("Error generating quote:", error)
      alert("Failed to generate quote. Please try again.")
    } finally {
      setIsGeneratingQuote(false)
    }
  }

  return (
    <div className=" bg-white rounded-lg w-auto mx-auto shadow-md mb-4">
      <div className="p-4 text-gray-900 font-bold text-lg flex justify-between items-center">
        Job Services
        <div className="flex gap-2">
          <div className="flex gap-8">
            {admin || !requiresApproval ? (
              <>
                <button
                  className={`p-1 text-xs rounded text-white ${
                    isDisabled || isGeneratingCSA ? "bg-gray-300 cursor-not-allowed" : "bg-orange-500 hover:bg-orange-600"
                  }`}
                  onClick={createCSA}
                  disabled={isDisabled || isGeneratingCSA}
                >
                  {isGeneratingCSA ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Generating...
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 pr-2">
                      <MdAssignment size={18} className="ml-1" />
                      CSA
                    </div>
                  )}
                </button>
                <button
                  className={`p-1 text-xs rounded text-white ${
                    isDisabled || isGeneratingQuote ? "bg-gray-300 cursor-not-allowed" : "bg-blue-500 hover:bg-blue-600"
                  }`}
                  onClick={createQuote}
                  disabled={isDisabled || isGeneratingQuote}
                >
                  {isGeneratingQuote ? (
                    <div className="flex items-center gap-1">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Generating...
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 pr-2">
                      <MdRequestQuote size={18} className="ml-1" />
                      Quote
                    </div>
                  )}
                </button>
              </>
            ) : admin || !requiresApproval ? null : (
              <button className="p-1 text-xs rounded text-white bg-gray-300 cursor-not-allowed" disabled={true}>
                Requires Approval
              </button>
            )}
            {
              <button
                className={`p-2 text-xs rounded text-white ${
                  isGeneratingInvoice ? "bg-gray-300 cursor-not-allowed" : "bg-green-500 hover:bg-green-600"
                }`}
                onClick={() => createInvoice(job.services, true)}
                disabled={isGeneratingInvoice}
              >
                {isGeneratingInvoice ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Generating...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <MdReceipt size={18} />
                    Invoice Job
                  </div>
                )}
              </button>
            }
          </div>
          <div className="flex gap-4 w-24 justify-end">
            {isEditing ? (
              <>
                <button onClick={handleSave} className="text-green-500 hover:text-green-700 mr-2">
                  <HiCheck size={26} />
                </button>
                <button onClick={handleCancel} className="text-red-500 hover:text-red-700">
                  <HiX size={26} />
                </button>
              </>
            ) : (
              <button onClick={() => setIsEditing(true)} className="text-blue-500 hover:text-blue-700">
                <HiPencilAlt size={26} />
              </button>
            )}
          </div>
        </div>
      </div>
      <table className="w-full text-left table-auto border-collapse border border-gray-300">
        <thead>
          <tr className="bg-gray-100 text-gray-900">
            <th className="p-4 border border-gray-300 text-gray-900 w-1">
              <div className="flex items-center gap-2">
                <span>Select</span>
                <input
                  type="checkbox"
                  checked={selectedServices.length === editedServices.length && editedServices.length > 0}
                  onChange={toggleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            </th>
            <th className="p-4 border border-gray-300 text-gray-900 w-2/12">Name</th>
            <th className="p-4 border border-gray-300 text-gray-900 w-1">Quantity</th>
            <th className="p-4 border border-gray-300 text-gray-900 w-1">Cost</th>
            <th className="p-4 border border-gray-300 text-gray-900 w-1">Sell</th>
            <th className="p-4 border border-gray-300 text-gray-900 w-1">
              <div className="flex flex-row gap-2 items-center">
                <span>Margin</span>
                <select value={marginUnit} onChange={e => setMarginUnit(e.target.value)} className="text-sm border border-gray-300 rounded p-1">
                  <option value="%">%</option>
                  <option value="$">$</option>
                </select>
              </div>
            </th>
            <th className="p-4 border border-gray-300 text-gray-900 w-1">Subtotal</th>
            <th className="p-4 border border-gray-300 text-gray-900 w-2/12">CSA Status</th>
            <th className="p-4 border border-gray-300 text-gray-900 w-1/12">Work Completed</th>
            {/* <th className="p-4 border border-gray-300 text-gray-900 w-2/12">Quote Status</th> */}
            <th className="p-4 border border-gray-300 text-gray-900 w-2/12">Invoice Status</th>
            <th className="p-4 border border-gray-300 text-gray-900 w-1/12">Actions</th>
          </tr>
        </thead>
        <tbody>
          {editedServices &&
            editedServices?.map((service, index) => (
              <React.Fragment key={service.id + index}>
                <tr className={`${expandedRow === index ? "bg-gray-100" : "hover:bg-gray-50"} cursor-pointer`}>
                  <td className="p-4 border border-gray-300 text-center text-gray-900">
                    <input
                      type="checkbox"
                      checked={selectedServices.includes(service.id)}
                      disabled={service?.workCompleted}
                      onChange={() => toggleRowSelection(service.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </td>
                  <td className="p-4 border border-gray-300">
                    {isEditing && !isLocked(service) ? (
                      <input
                        type="text"
                        value={service.name === "" ? "" : service.name || ""}
                        onChange={e => handleFieldChange(index, "name", e.target.value)}
                        className="w-full border border-gray-300 rounded p-2"
                      />
                    ) : (
                      service.name
                    )}
                  </td>
                  <td className="p-4 border border-gray-300 text-gray-900">
                    {isEditing && !isLocked(service) ? (
                      <input
                        type="number"
                        value={service.quantity || 1}
                        onChange={e => handleFieldChange(index, "quantity", parseFloat(e.target.value) || 1)}
                        className="w-full border border-gray-300 rounded p-2 cursor-default"
                        readOnly={false}
                        step="1"
                        min="1"
                        onKeyDown={e => e.preventDefault()}
                      />
                    ) : (
                      service.quantity || 1
                    )}
                  </td>
                  <td className="p-4 border border-gray-300 text-gray-900">
                    {isEditing && !isLocked(service) ? (
                      <input
                        type="text"
                        value={service.cost === "" ? "" : service.cost}
                        onChange={e => {
                          const value = e.target.value.replace(/,/g, "").trim() // Remove commas
                          // Allow negative numbers, empty string, valid numbers, or decimals in progress
                          if (value === "" || /^-?\d*\.?\d*$/.test(value)) {
                            handleFieldChange(index, "cost", value)
                          }
                        }}
                        onBlur={e => {
                          // Convert final value to number for storage
                          const value = parseFloat(e.target.value.replace(/,/g, ""))
                          handleFieldChange(index, "cost", isNaN(value) ? "" : value)
                        }}
                        className="w-full border border-gray-300 rounded p-2 text-right"
                      />
                    ) : // Display formatted string
                    service.cost === "" ? (
                      ""
                    ) : (
                      `$${parseFloat(service.cost).toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}`
                    )}
                  </td>
                  <td className="p-4 border border-gray-300 text-gray-900">
                    {isEditing && !isLocked(service) ? (
                      <input
                        type="text"
                        value={service.sell === "" ? "" : service.sell}
                        onChange={e => {
                          const value = e.target.value.replace(/,/g, "").trim() // Remove commas
                          // Allow negative numbers, empty string, valid numbers, or decimals in progress
                          if (value === "" || /^-?\d*\.?\d*$/.test(value)) {
                            handleFieldChange(index, "sell", value)
                          }
                        }}
                        onBlur={e => {
                          // Convert final value to number for storage
                          const value = parseFloat(e.target.value.replace(/,/g, ""))
                          handleFieldChange(index, "sell", isNaN(value) ? "" : value)
                        }}
                        className="w-full border border-gray-300 rounded p-2 text-right"
                      />
                    ) : // Display formatted string
                    service.sell === "" ? (
                      ""
                    ) : (
                      `$${parseFloat(service.sell).toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}`
                    )}
                  </td>
                  <td className="p-4 border border-gray-300 text-gray-900">
                    {isEditing && !isLocked(service) ? (
                      <input
                        type="text"
                        value={
                          // If user has explicitly set a margin value, use that
                          service.marginValue !== undefined && service.marginValue !== null
                            ? service.marginValue
                            : // Otherwise calculate it based on cost and sell
                              calculateMargin(service.cost, service.sell, marginUnit, service.quantity).replace(/[%$]/g, "")
                        }
                        onChange={e => {
                          const value = e.target.value.replace(/,/g, "").trim() // Remove commas
                          // Allow negative numbers, empty string, valid numbers, or decimals in progress
                          if (value === "" || /^-?\d*\.?\d*$/.test(value)) {
                            handleMarginChange(index, value)
                          }
                        }}
                        onBlur={e => {
                          // Convert final value to number for storage
                          const value = e.target.value.replace(/,/g, "")
                          const numValue = parseFloat(value)
                          handleMarginChange(index, isNaN(numValue) ? "" : numValue)
                        }}
                        className="w-full border border-gray-300 rounded p-2 text-right"
                      />
                    ) : (
                      calculateMargin(service.cost, service.sell, marginUnit, service.quantity)
                    )}
                  </td>
                  <td className="p-4 border border-gray-300 text-gray-900">
                    $
                    {(() => {
                      const sell = service.sell === "" ? 0 : parseFloat(service.sell)
                      const quantity = parseInt(service.quantity || 1)
                      const isNegative = service.sell?.toString().includes("-")
                      const total = (isNegative ? -Math.abs(sell) : sell) * quantity
                      return total.toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })
                    })()}
                  </td>
                  <td className="p-4 border border-gray-300 text-gray-900">
                    {csas.some(csa => csa.services.some(s => s.id === service.id)) ? (
                      (() => {
                        const relatedCSAs = csas.filter(csa => csa.services.some(s => s.id === service.id))
                        const latestCSA = relatedCSAs.sort((a, b) => b.createdAt - a.createdAt)[0]
                        return latestCSA ? (
                          <a href={latestCSA.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500">
                            <div className="flex items-center gap-1 text-sm">
                              <FaEye size={18} /> {latestCSA.displayId || "N/A"}
                            </div>
                          </a>
                        ) : (
                          <span className="text-gray-500 italic">No CSA available</span>
                        )
                      })()
                    ) : (
                      <span className="text-gray-500 italic">No CSA available</span>
                    )}
                  </td>
                  <td className="p-4 border border-gray-300 text-gray-900">
                    {service?.workCompleted ? (
                      <div className="flex items-center gap-1 justify-center">
                        <FaCheck size={30} className="text-green-500 self-center" />
                      </div>
                    ) : // <span className="text-green-600 font-bold">✅ Completed</span> // Display a locked state
                    service?.csaGenerated ? (
                      <button
                        onClick={() => handleMarkCompleted(service.id)}
                        className="p-1 rounded text-white bg-blue-500 hover:bg-blue-600 text-sm"
                      >
                        Mark Completed
                      </button>
                    ) : (
                      <span className="text-gray-500 italic">N/A (CSA Required)</span>
                    )}
                  </td>
                  {/* <td className="p-4 border border-gray-300 text-gray-900">
                    {quotes.some(quote => quote.services.some(s => s.id === service.id)) ? (
                      (() => {
                        const relatedQuotes = quotes.reduce((acc, quote) => {
                          const relatedServices = quote.services.filter(s => s.id === service.id)
                          if (relatedServices.length > 0) {
                            acc.push({ ...quote, services: relatedServices })
                          }
                          return acc
                        }, [])
                        const latestQuote = relatedQuotes.sort((a, b) => b.createdAt - a.createdAt)[0]

                        return latestQuote ? (
                          <>
                            <a href={latestQuote.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500">
                              <div className="flex items-center gap-1 text-sm">
                                <FaEye size={18} /> {latestQuote.displayId || "N/A"}
                              </div>
                            </a>
                          </>
                        ) : (
                          <span className="text-gray-500 italic">No Quotes available</span>
                        )
                      })()
                    ) : selectedServices.includes(service.id) && (admin || !requiresApproval) ? (
                      <button onClick={createQuote} className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600">
                        {`${service?.quoteGenerated ? "New" : "Generate"}`} Quote
                      </button>
                    ) : null}
                  </td> */}
                  <td className="p-4 border border-gray-300 text-gray-900">
                    {invoices.some(inv => inv.services.some(s => s.id === service.id)) ? (
                      (() => {
                        const relatedInvoices = invoices.reduce((acc, inv) => {
                          const relatedServices = inv.services.filter(s => s.id === service.id)
                          if (relatedServices.length > 0) {
                            acc.push({ ...inv, services: relatedServices })
                          }
                          return acc
                        }, [])
                        const latestInvoice = relatedInvoices.sort((a, b) => b.createdAt - a.createdAt)[0]

                        return latestInvoice ? (
                          <>
                            <a href={latestInvoice?.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500">
                              <div className="flex items-center gap-1 text-sm">
                                <FaEye size={18} /> {latestInvoice?.displayId || "N/A"}
                              </div>
                            </a>
                          </>
                        ) : (
                          <span className="text-gray-500 italic">No Invoices available</span>
                        )
                      })()
                    ) : service.csaGenerated ? (
                      <>
                        {service.invoiceGenerated ? (
                          <div className="flex items-center gap-1">
                            <FaEye size={18} /> {"N/A"}
                          </div>
                        ) : (
                          <button onClick={() => createInvoice(service)} className="px-4 py-2 rounded text-white bg-blue-500 hover:bg-blue-600">
                            {`${service?.invoiceGenerated ? "New" : "Generate"}`} Invoice
                          </button>
                        )}
                      </>
                    ) : (
                      <span className="text-red-500 text-sm">❌ No Invoice</span>
                    )}
                  </td>
                  <td className="p-4 border border-gray-300">
                    <div className="flex justify-evenly gap-2">
                      <div className="flex-col">
                        <button onClick={() => toggleRowExpansion(index)} className="text-blue-500 hover:text-blue-700">
                          {expandedRow === index ? <HiChevronUp size={24} /> : <HiChevronDown size={24} />}
                        </button>
                      </div>
                      <button onClick={e => removeService(e, service.id)} className="text-red-500 hover:text-red-700">
                        <FaTrashAlt size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
                {expandedRow === index && (
                  <tr className="bg-gray-50 text-gray-700">
                    <td colSpan={11} className="p-4 border border-gray-300 bg-gray-50">
                      <label className="block text-gray-700 mb-2 font-bold">Description</label>
                      {isEditing && !service?.workCompleted ? (
                        <textarea
                          value={service.description || ""}
                          onChange={e => handleFieldChange(index, "description", e.target.value)}
                          className="w-full border border-gray-300 rounded p-2"
                          rows={4}
                        />
                      ) : (
                        service.description || "No description available."
                      )}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          <tr className="font-bold bg-gray-100 text-gray-900">
            <td colSpan="2" className="p-4 border border-gray-300 text-left">
              Subtotal
            </td>
            <td className="p-4 border border-gray-300">{editedServices.reduce((sum, service) => sum + Number(service.quantity || 1), 0)}</td>
            <td className="p-4 border border-gray-300">
              $
              {(() => {
                const total = editedServices.reduce((sum, service) => {
                  const cost = Number(service.cost || 0)
                  const quantity = Number(service.quantity || 1)
                  const isNegative = service.cost?.toString().includes("-")
                  return sum + (isNegative ? -Math.abs(cost) : cost) * quantity
                }, 0)
                return total.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
              })()}
            </td>
            <td className="p-4 border border-gray-300">
              $
              {(() => {
                const total = editedServices.reduce((sum, service) => {
                  const sell = Number(service.sell || 0)
                  const quantity = Number(service.quantity || 1)
                  const isNegative = service.sell?.toString().includes("-")
                  return sum + (isNegative ? -Math.abs(sell) : sell) * quantity
                }, 0)
                return total.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
              })()}
            </td>
            <td className="p-4 border border-gray-300">
              {marginUnit === "%"
                ? (() => {
                    const totalCost = editedServices.reduce((sum, service) => {
                      const cost = Number(service.cost || 0)
                      const quantity = Number(service.quantity || 1)
                      const isNegative = service.cost?.toString().includes("-")
                      return sum + (isNegative ? -Math.abs(cost) : cost) * quantity
                    }, 0)
                    const totalSell = editedServices.reduce((sum, service) => {
                      const sell = Number(service.sell || 0)
                      const quantity = Number(service.quantity || 1)
                      const isNegative = service.sell?.toString().includes("-")
                      return sum + (isNegative ? -Math.abs(sell) : sell) * quantity
                    }, 0)
                    const marginPercent = totalCost === 0 ? 0 : ((totalSell - totalCost) / Math.abs(totalCost)) * 100
                    return (
                      marginPercent.toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      }) + "%"
                    )
                  })()
                : "$" +
                  (() => {
                    const total = editedServices.reduce((sum, service) => {
                      const cost = Number(service.cost || 0)
                      const quantity = Number(service.quantity || 1)
                      const isNegative = service.cost?.toString().includes("-")
                      return sum + (isNegative ? -Math.abs(cost) : cost) * quantity
                    }, 0)
                    return total.toLocaleString("en-US", {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })
                  })()}
            </td>
            <td className="p-4 border border-gray-300">
              $
              {(() => {
                const total = editedServices.reduce((sum, service) => {
                  const sell = Number(service.sell || 0)
                  const quantity = Number(service.quantity || 1)
                  const isNegative = service.sell?.toString().includes("-")
                  return sum + (isNegative ? -Math.abs(sell) : sell) * quantity
                }, 0)
                return total.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })
              })()}
            </td>
            <td colSpan={1} className="p-4 border border-gray-300">
              <div className="flex-col">
                <p className="text-gray-500 text-xs">Complete</p>
                {calculatePercentage(editedServices, "csaGenerated")}
              </div>
            </td>
            <td colSpan={1} className="p-4 border border-gray-300">
              <p className="text-gray-500 text-xs">Complete</p>
              {calculatePercentage(editedServices, "workCompleted")}
            </td>
            <td colSpan={1} className="p-4 border border-gray-300">
              <p className="text-gray-500 text-xs">Complete</p>
              {calculatePercentage(editedServices, "quoteGenerated")}
            </td>
            <td colSpan={1} className="p-4 border border-gray-300">
              <p className="text-gray-500 text-xs">Complete</p>
              {calculatePercentage(editedServices, "invoiceGenerated")}
            </td>
            {/* <td colSpan={3} className="p-4 border border-gray-300"></td> */}
          </tr>
        </tbody>
      </table>
      {isPreviewOpen && pdfUrls.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white w-11/12 max-w-5xl rounded shadow-lg overflow-hidden p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold text-gray-900">PDF Preview</h2>
              <button onClick={() => setIsPreviewOpen(false)} className="text-red-500 hover:text-red-700">
                Close
              </button>
            </div>
            <div className="mb-4">
              <iframe src={pdfUrls[currentPage]} title={`PDF Preview ${currentPage + 1}`} className="w-full h-[80vh] border rounded-lg"></iframe>
            </div>
            <div className="flex justify-between items-center">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 0))}
                disabled={currentPage === 0}
                className={`px-4 py-2 rounded ${
                  currentPage === 0 ? "bg-gray-300 text-gray-500 cursor-not-allowed" : "bg-blue-500 text-white hover:bg-blue-600"
                }`}
              >
                Previous
              </button>
              <span className="text-gray-900">{`PDF ${currentPage + 1} of ${pdfUrls.length}`}</span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, pdfUrls.length - 1))}
                disabled={currentPage === pdfUrls.length - 1}
                className={`px-4 py-2 rounded ${
                  currentPage === pdfUrls.length - 1 ? "bg-gray-300 text-gray-500 cursor-not-allowed" : "bg-blue-500 text-white hover:bg-blue-600"
                }`}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
      {isPreviewOpen && invoiceUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white w-11/12 max-w-5xl rounded shadow-lg overflow-hidden p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold text-gray-900">PDF Preview</h2>
              <button
                onClick={() => {
                  setIsPreviewOpen(false)
                  setInvoiceUrl(null)
                }}
                className="text-red-500 hover:text-red-700"
              >
                Close
              </button>
            </div>
            <div className="mb-4">
              <iframe src={invoiceUrl} title={`PDF Preview`} className="w-full h-[80vh] border rounded-lg"></iframe>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default JobServicesTable
