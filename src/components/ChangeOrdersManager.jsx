import React, { useEffect, useState } from "react"
import { collection, getDocs, doc, updateDoc, setDoc, getDoc, onSnapshot, deleteDoc } from "firebase/firestore"
import { ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { Hi<PERSON>hevronDown, HiChevronUp, <PERSON><PERSON>heck, HiX, HiPencilAlt } from "react-icons/hi"
import { generateChangeOrderQuotePDF } from "@/utils/generateChangeOrderQuotePDF"
import { getNextDisplayId } from "@/utils/getNextDisplayId"
import { MdRequestQuote } from "react-icons/md"
import * as Tooltip from "@radix-ui/react-tooltip"
import StatusComponent from "@/components/StatusComponent"
import { calculateSubtotal } from "@/utils/calculateSubtotal"
import { FaPlus, FaTrashAlt } from "react-icons/fa"
import { useSelector } from "react-redux"
import { db, storage } from "@/firebase"
import { Timestamp } from "firebase/firestore"
import ServicePicker from "@/components/ServicePicker"
import AutocompleteInput from "@/components/AutocompleteInput"
import { toTitleCase } from "@/utils/toTitleCase"

const ChangeOrdersManager = ({ job, companyId }) => {
  const [changeOrders, setChangeOrders] = useState([])
  const [editingChangeOrderId, setEditingChangeOrderId] = useState(null)
  const [expandedRows, setExpandedRows] = useState({})
  const [pdfUrlForOrder, setPdfUrlForOrder] = useState({}) // Map changeOrder id to pdfUrl
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [jobDetails, setJobDetails] = useState(null)
  const [currentPreviewOrderId, setCurrentPreviewOrderId] = useState(null)
  const [pdfFilenameForOrder, setPdfFilenameForOrder] = useState({})
  const [approvedBy, setApprovedBy] = useState({}) // Track approvedBy for each change order
  const [services, setServices] = useState([])
  const [customers, setCustomers] = useState([])
  const company = useSelector(state => state.company)
  const totalQuantity = services => services.reduce((sum, service) => sum + Number(service.quantity || 0), 0)
  const totalCost = services => services.reduce((sum, service) => sum + Number(service.cost || 0) * Number(service.quantity || 1), 0)
  const totalSell = services => services.reduce((sum, service) => sum + Number(service.sell || 0) * Number(service.quantity || 1), 0)

  // Helper function to sort change orders in ascending order (oldest first)
  const sortChangeOrders = orders => {
    return [...orders].sort((a, b) => {
      // Convert Firestore timestamps or date strings to Date objects
      const dateA = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt?.seconds * 1000 || a.createdAt)
      const dateB = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt?.seconds * 1000 || b.createdAt)
      return dateA - dateB // Changed from dateB - dateA to dateA - dateB for ascending order
    })
  }

  // Fetch change orders from Firestore.
  useEffect(() => {
    if (!companyId || !job?.id) return

    // Create reference to change orders collection
    const changeOrdersRef = collection(db, `companies/${companyId}/jobs/${job.id}/changeOrders`)

    // Set up real-time listener
    const unsubscribe = onSnapshot(
      changeOrdersRef,
      snapshot => {
        try {
          const orders = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))
          setChangeOrders(sortChangeOrders(orders))
        } catch (error) {
          console.error("Error processing change orders update:", error.message)
        }
      },
      error => {
        console.error("Error setting up change orders listener:", error)
      },
    )

    // Clean up listener on unmount
    return () => unsubscribe()
  }, [companyId, job])

  // Fetch job details from Firestore.
  useEffect(() => {
    const fetchJobDetails = async () => {
      if (!companyId || !job?.id) return
      try {
        const jobDoc = await getDoc(doc(db, `companies/${companyId}/jobs/${job.id}`))
        if (jobDoc.exists()) {
          const jobData = jobDoc.data()
          // Update the job data in your state or props
          // You might need to lift this state up to the parent component
          setJobDetails(jobData)
        }
      } catch (error) {
        console.error("Error fetching job details:", error)
      }
    }

    fetchJobDetails()
  }, [companyId, job?.id])

  // Add new useEffect for fetching services and rates
  useEffect(() => {
    if (!companyId || !job?.customer?.id) return

    const fetchServicesAndRates = async () => {
      try {
        // Fetch services
        const servicesRef = collection(db, `companies/${companyId}/services`)
        const servicesSnapshot = await getDocs(servicesRef)
        const servicesList = servicesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }))

        // Fetch customer for rates
        const customerRef = doc(db, `companies/${companyId}/customers/${job.customer.id}`)
        const customerDoc = await getDoc(customerRef)
        const customerData = customerDoc.data()

        // Convert rates to suggestion format
        const ratesList = Object.entries(customerData?.rates || {}).map(([key, value]) => ({
          id: key,
          name: toTitleCase(key),
          sell: value,
          cost: 0,
          isRate: true,
        }))

        setServices([...servicesList, ...ratesList])
      } catch (error) {
        console.error("Error fetching services and rates:", error)
      }
    }

    fetchServicesAndRates()
  }, [companyId, job?.customer?.id])

  // Update handleServiceFieldChange to handle suggestion selection
  const handleServiceFieldChange = (orderId, index, field, value, fullSuggestion = null) => {
    setChangeOrders(prevOrders =>
      prevOrders.map(order => {
        if (order.id === orderId) {
          const updatedServices = [...order.services]
          if (fullSuggestion) {
            // Update multiple fields when a suggestion is selected
            updatedServices[index] = {
              ...updatedServices[index],
              name: fullSuggestion.name,
              sell: fullSuggestion.sell || updatedServices[index].sell,
              cost: fullSuggestion.cost || updatedServices[index].cost,
            }
          } else {
            // Update single field for manual input
            updatedServices[index] = { ...updatedServices[index], [field]: value }
          }
          return { ...order, services: updatedServices }
        }
        return order
      }),
    )
  }

  // Add a new service row to a given change order.
  const addServiceRow = async (orderId, service = null) => {
    try {
      const newRow = { ...service, quantity: 1 } || {
        id: Date.now().toString(), // temporary ID
        name: "",
        quantity: 1,
        cost: 0,
        sell: 0,
        description: "",
      }

      // Get the current change order
      const currentOrder = changeOrders.find(order => order.id === orderId)
      if (!currentOrder) return

      // Create updated services array with the new row
      const updatedServices = [...currentOrder.services, newRow]

      // Update Firestore document
      const orderDocRef = doc(db, `companies/${companyId}/jobs/${job.id}/changeOrders/${orderId}`)
      await updateDoc(orderDocRef, {
        services: updatedServices,
      })

      // Update local state
      setChangeOrders(prevOrders =>
        prevOrders.map(order => {
          if (order.id === orderId) {
            return { ...order, services: updatedServices }
          }
          return order
        }),
      )
    } catch (error) {
      console.error("Error adding service row:", error)
      alert("Failed to add line item. Please try again.")
    }
  }

  // Remove a service row from a change order.
  const removeServiceRow = async (orderId, index) => {
    // if (!window.confirm("Are you sure you want to delete this line item?")) {
    //   return
    // }

    try {
      // Get the current change order
      const currentOrder = changeOrders.find(order => order.id === orderId)
      if (!currentOrder) return

      // Create updated services array without the deleted item
      const updatedServices = currentOrder.services.filter((_, i) => i !== index)

      // Update Firestore document
      const orderDocRef = doc(db, `companies/${companyId}/jobs/${job.id}/changeOrders/${orderId}`)
      await updateDoc(orderDocRef, {
        services: updatedServices,
      })

      // Update local state
      setChangeOrders(prevOrders =>
        prevOrders.map(order => {
          if (order.id === orderId) {
            return { ...order, services: updatedServices }
          }
          return order
        }),
      )
    } catch (error) {
      console.error("Error removing service row:", error)
      alert("Failed to delete line item. Please try again.")
    }
  }

  // Save an edited change order back to Firestore.
  const handleSaveChangeOrder = async orderId => {
    try {
      const orderToSave = changeOrders.find(order => order.id === orderId)
      if (!orderToSave) return
      // Validate required fields in each service.
      const valid = orderToSave.services.every(service => service.name && service.cost !== "" && service.sell !== "")
      if (!valid) {
        alert("Please ensure each service row has a Name, Cost, and Sell value.")
        return
      }
      const updatedServices = orderToSave.services.map(service => ({
        ...service,
        cost: service.cost === "" ? 0 : Math.round(Number(service.cost) * 100) / 100,
        sell: service.sell === "" ? 0 : Math.round(Number(service.sell) * 100) / 100,
      }))
      const updatedOrder = { ...orderToSave, services: updatedServices }
      const orderDocRef = doc(db, `companies/${companyId}/jobs/${job.id}/changeOrders`, orderId)
      await updateDoc(orderDocRef, updatedOrder)
      setChangeOrders(prev => sortChangeOrders(prev.map(order => (order.id === orderId ? updatedOrder : order))))
      setEditingChangeOrderId(null)
    } catch (error) {
      console.error("Error saving change order:", error.message)
    }
  }

  // Create a new change order document in Firestore.
  const handleCreateNewChangeOrder = async () => {
    if (!companyId || !job?.id) return
    try {
      // Get the next display ID first
      const displayId = await getNextDisplayId("changeOrder", companyId)

      const newOrderData = {
        createdAt: new Date(),
        status: "New",
        displayId,
        services: [
          {
            id: Date.now().toString(),
            name: "",
            quantity: 1,
            cost: 0,
            sell: 0,
            description: "",
          },
        ],
      }

      const jobDocRef = doc(db, `companies/${companyId}/jobs`, job.id)
      const changeOrdersCollectionRef = collection(jobDocRef, "changeOrders")
      const newDocRef = doc(changeOrdersCollectionRef)
      newOrderData.id = newDocRef.id
      await setDoc(newDocRef, newOrderData)
      // The onSnapshot listener will handle updating the state
      setEditingChangeOrderId(newDocRef.id)
    } catch (error) {
      console.error("Error creating new change order:", error.message)
      alert("Failed to create new change order. Please try again.")
    }
  }

  // Generate a PDF for a change order by merging original opportunity services and change order services.
  // This function calls the utility function from File 2.
  const handleGeneratePDF = async order => {
    try {
      const jobWithServices = {
        ...job,
        services: (job.services || job.items || []).map(service => ({
          name: service.name || service.description || "",
          quantity: service.quantity || 1,
          price: service.price || service.sell || service.unitPrice || 0,
          total: (service.quantity || 1) * (service.price || service.sell || service.unitPrice || 0),
          description: service.description || "",
        })),
        projectId: job.projectId || job.id,
        clientName: job.clientName || job.customer?.name || "",
      }

      const formattedChangeOrder = {
        ...order,
        services: order.services.map(service => ({
          name: service.name || "",
          quantity: service.quantity || 1,
          sell: service.sell || 0,
          total: (service.quantity || 1) * (service.sell || 0),
          description: service.description || "",
        })),
      }

      const changeOrderId = await getNextDisplayId("changeOrder", companyId)

      // Update the change order with the display ID in Firestore
      const orderDocRef = doc(db, `companies/${companyId}/jobs/${job.id}/changeOrders/${order.id}`)
      await updateDoc(orderDocRef, {
        displayId: changeOrderId,
      })

      const filename = `${changeOrderId}.pdf`
      const { file } = await generateChangeOrderQuotePDF(jobWithServices, { ...formattedChangeOrder, displayId: changeOrderId }, company, filename)

      // Upload the PDF to Firebase Storage
      const storageRef = ref(storage, `companies/${companyId}/jobs/${job.id}/changeOrders/${filename}`)
      await uploadBytes(storageRef, file)
      const downloadUrl = await getDownloadURL(storageRef)

      // Update Firestore with the storage URL and status
      await updateDoc(orderDocRef, {
        pdfUrl: downloadUrl,
        filename,
        status: "Quoted",
        quotedAt: Timestamp.now(),
      })

      // Update local state
      setChangeOrders(prevOrders =>
        prevOrders.map(ord =>
          ord.id === order.id
            ? {
                ...ord,
                displayId: changeOrderId,
                pdfUrl: downloadUrl,
                filename,
                status: "Quoted",
                quotedAt: Timestamp.now(),
              }
            : ord,
        ),
      )

      // Update the PDF URL mapping for preview
      setPdfUrlForOrder(prev => ({
        ...prev,
        [order.id]: downloadUrl,
      }))

      setPdfFilenameForOrder(prev => ({
        ...prev,
        [order.id]: filename,
      }))

      // Preview the newly generated PDF
      setIsPreviewOpen(true)
      setCurrentPreviewOrderId(order.id)
    } catch (error) {
      console.error("Error generating PDF:", error)
      alert("Error generating PDF: " + error.message)
    }
  }

  // Add this function to handle approval
  const handleApproval = async (orderId, approverName) => {
    try {
      const orderDocRef = doc(db, `companies/${companyId}/jobs/${job.id}/changeOrders/${orderId}`)
      const timestamp = Timestamp.now() // Create a proper Firestore timestamp
      await updateDoc(orderDocRef, {
        approvedBy: approverName,
        status: "Approved",
        approvedAt: timestamp,
      })

      setChangeOrders(prevOrders =>
        prevOrders.map(order => (order.id === orderId ? { ...order, approvedBy: approverName, status: "Approved", approvedAt: timestamp } : order)),
      )

      setApprovedBy(prev => ({ ...prev, [orderId]: approverName }))
    } catch (error) {
      console.error("Error updating approval:", error)
      alert("Failed to update approval status")
    }
  }

  const handleStatusChange = async (orderId, newValue) => {
    try {
      const orderDocRef = doc(db, `companies/${companyId}/jobs/${job.id}/changeOrders/${orderId}`)
      await updateDoc(orderDocRef, {
        status: newValue,
      })
    } catch (error) {
      console.error("Error updating status:", error)
      alert("Failed to update status")
    }
  }

  const handleDeleteChangeOrder = async orderId => {
    if (!window.confirm("Are you sure you want to delete this change order? This action cannot be undone.")) {
      return
    }

    try {
      const orderDocRef = doc(db, `companies/${companyId}/jobs/${job.id}/changeOrders/${orderId}`)
      await deleteDoc(orderDocRef)
      // No need to update state as the onSnapshot listener will handle it
    } catch (error) {
      console.error("Error deleting change order:", error)
      alert("Failed to delete change order. Please try again.")
    }
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Change Orders</h2>
      {changeOrders.length === 0 ? (
        <p>No change orders found.</p>
      ) : (
        changeOrders.map((order, orderIndex) => {
          const isEditing = editingChangeOrderId === order.id
          const orderApproved = order.status?.toLowerCase() === "approved" ?? false
          return (
            <div key={order.id} className="mb-6 border p-4 rounded">
              <div className="flex justify-between items-center mb-4">
                {/* Left Section: Label, Status, and Approval */}
                <div className="flex items-center gap-4">
                  <div className="text-gray-900">
                    <strong>Change Order:</strong>
                    <span>{`#${orderIndex + 1}`}</span>
                    <div className="flex items-center gap-2">
                      <span>ID: {order.displayId}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {order.status !== "Approved" ? (
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          placeholder="Enter Approver Name"
                          value={approvedBy[order.id] || ""}
                          onChange={e =>
                            setApprovedBy(prev => ({
                              ...prev,
                              [order.id]: e.target.value,
                            }))
                          }
                          className="border rounded px-2 py-1 text-sm"
                        />
                        <button
                          onClick={() => handleApproval(order.id, approvedBy[order.id])}
                          disabled={!approvedBy[order.id]}
                          className="bg-green-500 text-white px-2 py-1 rounded text-sm disabled:bg-gray-300"
                        >
                          Approve
                        </button>
                      </div>
                    ) : null}
                  </div>
                </div>

                {/* Right Section: Action Icons */}
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {order.status === "Approved" ? (
                      <div className="text-sm text-gray-600">
                        Approved by: {order.approvedBy}
                        {order.approvedAt && <span className="ml-1">on {new Date(order.approvedAt?.seconds * 1000).toLocaleDateString()}</span>}
                      </div>
                    ) : (
                      <StatusComponent
                        type="changeOrder"
                        status={order.status || "New"}
                        onChange={newValue => {
                          if (newValue === "Approved") {
                            if (approvedBy[order.id] && order.approvedBy) {
                              handleStatusChange(order.id, newValue)
                            } else {
                              alert("Please enter an approver name before approving the change order.")
                            }
                          }
                        }}
                      />
                    )}
                  </div>
                  {isEditing ? (
                    <>
                      <button onClick={() => handleSaveChangeOrder(order.id)} className="text-green-500 hover:text-green-700">
                        <HiCheck size={26} />
                      </button>
                      <button onClick={() => setEditingChangeOrderId(null)} className="text-red-500 hover:text-red-700">
                        <HiX size={26} />
                      </button>
                    </>
                  ) : (
                    <div className="flex items-end gap-2 items-center">
                      <Tooltip.Root>
                        <Tooltip.Trigger asChild>
                          <div onClick={() => handleGeneratePDF(order)} className="px-2 py-1 rounded cursor-pointer">
                            <MdRequestQuote size={26} className="text-green-500 hover:text-green-600" />
                          </div>
                        </Tooltip.Trigger>
                        <Tooltip.Content className="bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg">Generate Quote</Tooltip.Content>
                      </Tooltip.Root>
                      <Tooltip.Root>
                        <Tooltip.Trigger asChild>
                          {!orderApproved ? (
                            <div onClick={() => setEditingChangeOrderId(order.id)} className="text-blue-500 hover:text-blue-700 cursor-pointer">
                              <HiPencilAlt size={26} />
                            </div>
                          ) : null}
                        </Tooltip.Trigger>
                        <Tooltip.Content className="bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg">Edit Change Order</Tooltip.Content>
                      </Tooltip.Root>
                      {!orderApproved && (
                        <Tooltip.Root>
                          <Tooltip.Trigger asChild>
                            <div
                              onClick={() => handleDeleteChangeOrder(order.id)}
                              className="text-red-500 hover:text-red-700 cursor-pointer px-2 py-1 rounded"
                            >
                              <FaTrashAlt size={20} />
                            </div>
                          </Tooltip.Trigger>
                          <Tooltip.Content className="bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg">
                            Delete Change Order
                          </Tooltip.Content>
                        </Tooltip.Root>
                      )}
                    </div>
                  )}
                </div>
              </div>
              <div className="overflow-x-auto relative">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="p-2 border w-1/12">#</th>
                      <th className="p-2 border w-4/12">Name</th>
                      <th className="p-2 border w-1/12">Quantity</th>
                      <th className="p-2 border w-1/12">Cost</th>
                      <th className="p-2 border w-1/12">Sell</th>
                      <th className="p-2 border w-1/12">Total</th>
                      <th className="p-2 border w-1/12 text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {order.services.map((service, index) => {
                      const rowKey = `${order.id}-${index}`
                      const total = (Number(service.quantity) || 0) * (Number(service.sell) || 0)
                      return (
                        <React.Fragment key={service.id}>
                          <tr className={`${expandedRows[rowKey] ? "bg-gray-100" : "hover:bg-gray-50"}`}>
                            <td className="p-2 border text-center">{index + 1}</td>
                            <td className="p-2 border">
                              {isEditing ? (
                                <AutocompleteInput
                                  value={service.name}
                                  onChange={value => handleServiceFieldChange(order.id, index, "name", value)}
                                  onSelect={suggestion => handleServiceFieldChange(order.id, index, "name", suggestion.name, suggestion)}
                                  suggestions={services}
                                  className="w-full border rounded p-1"
                                />
                              ) : (
                                service.name
                              )}
                            </td>
                            <td className="p-2 border">
                              {isEditing ? (
                                <input
                                  type="number"
                                  value={service.quantity}
                                  onChange={e => handleServiceFieldChange(order.id, index, "quantity", e.target.value)}
                                  className="w-full border rounded p-1"
                                />
                              ) : (
                                service.quantity
                              )}
                            </td>
                            <td className="p-2 border">
                              {isEditing ? (
                                <input
                                  type="text"
                                  value={service.cost}
                                  onChange={e => handleServiceFieldChange(order.id, index, "cost", e.target.value)}
                                  className="w-full border rounded p-1"
                                />
                              ) : (
                                `$${parseFloat(service.cost).toLocaleString("en-US", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}`
                              )}
                            </td>
                            <td className="p-2 border">
                              {isEditing ? (
                                <input
                                  type="text"
                                  value={service.sell}
                                  onChange={e => handleServiceFieldChange(order.id, index, "sell", e.target.value)}
                                  className="w-full border rounded p-1"
                                />
                              ) : (
                                `$${parseFloat(service.sell).toLocaleString("en-US", {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                })}`
                              )}
                            </td>
                            <td className="p-2 border">
                              $
                              {total.toLocaleString("en-US", {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              })}
                            </td>
                            <td className="p-2 border text-center">
                              <div className="flex justify-evenly items-center">
                                <button
                                  onClick={() =>
                                    setExpandedRows(prev => ({
                                      ...prev,
                                      [rowKey]: !prev[rowKey],
                                    }))
                                  }
                                  className="text-blue-500 hover:text-blue-700"
                                >
                                  {expandedRows[rowKey] ? <HiChevronUp size={20} /> : <HiChevronDown size={20} />}
                                </button>
                                {!orderApproved ? (
                                  <button
                                    onClick={() => removeServiceRow(order.id, index)}
                                    className="text-red-500 hover:text-red-700"
                                    title="Delete line item"
                                  >
                                    <FaTrashAlt size={16} />
                                  </button>
                                ) : null}
                              </div>
                            </td>
                          </tr>
                          {expandedRows[rowKey] && (
                            <tr className="hover:bg-gray-50 items-center">
                              <td colSpan="6" className="p-2 border">
                                <div className="flex items-start ml-8">
                                  <label className="block text-gray-700 font-bold">Description:</label>
                                  <div className="border-l-4 border-blue-500 pr-2" />
                                  {isEditing ? (
                                    <textarea
                                      value={service.description}
                                      onChange={e => handleServiceFieldChange(order.id, index, "description", e.target.value)}
                                      className="w-full border rounded p-1 h-12"
                                      placeholder="Enter description"
                                    />
                                  ) : (
                                    service.description
                                  )}
                                </div>
                              </td>
                            </tr>
                          )}
                        </React.Fragment>
                      )
                    })}
                    {!orderApproved ? (
                      <tr>
                        <td colSpan="7" className="p-2 border text-center">
                          <div className="flex items-center justify-between">
                            <ServicePicker order={job} onAddService={service => addServiceRow(order.id, service)} />
                            {/* <button
                              onClick={() => addServiceRow(order.id)}
                              className="flex items-center justify-center text-blue-500 hover:text-blue-700"
                            >
                              <FaPlus className="mr-1" /> Add Blank Row
                            </button> */}
                          </div>
                        </td>
                      </tr>
                    ) : null}
                    <tr className="font-bold bg-gray-100">
                      <td colSpan="1" className="p-2 border text-right">
                        Subtotal:
                      </td>
                      <td colSpan="1" className="p-2 border text-right"></td>
                      <td className="p-2 border text-right">{totalQuantity(order.services)}</td>
                      <td className="p-2 border text-right">
                        ${totalCost(order.services).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </td>
                      <td className="p-2 border text-right">
                        ${totalSell(order.services).toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </td>
                      <td className="p-2 border text-right">
                        $
                        {order.services
                          .reduce((sum, service) => sum + (Number(service.quantity) || 0) * (Number(service.sell) || 0), 0)
                          .toLocaleString("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </td>
                      <td colSpan="2" className="p-2 border text-center">
                        {order.pdfUrl ? (
                          <a href={order.pdfUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">
                            View PDF
                          </a>
                        ) : (
                          "No PDF generated"
                        )}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )
        })
      )}
      <div className="flex justify-end mt-4">
        <button onClick={handleCreateNewChangeOrder} className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          New Change Order
        </button>
      </div>
      {isPreviewOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
          <div className="bg-white w-11/12 max-w-5xl rounded shadow-lg overflow-hidden p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-bold text-gray-900">PDF Preview</h2>
              <button
                onClick={() => {
                  setIsPreviewOpen(false)
                  setCurrentPreviewOrderId(null)
                }}
                className="text-red-500 hover:text-red-700"
              >
                Close
              </button>
            </div>
            <div className="mb-4">
              <a href={pdfUrlForOrder[currentPreviewOrderId]} download="ChangeOrder.pdf" className="text-blue-500 underline">
                Download as {pdfFilenameForOrder[currentPreviewOrderId]}
              </a>
              <iframe src={pdfUrlForOrder[currentPreviewOrderId]} title="PDF Preview" className="w-full h-[80vh] border rounded-lg"></iframe>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ChangeOrdersManager
